# Project Structure & Organization

## Root Directory Structure

```
├── src/                    # Source code
├── public/                 # Static assets (SVG icons)
├── .kiro/                  # Kiro AI assistant configuration
├── .next/                  # Next.js build output
├── node_modules/           # Dependencies
└── [config files]          # Various configuration files
```

## Source Code Organization (`src/`)

### App Directory (`src/app/`)
- **Next.js App Router**: Uses the new app directory structure
- `layout.tsx` - Root layout component
- `page.tsx` - Home page component
- `globals.css` - Global styles and Tailwind imports
- `favicon.ico` - Application favicon

### Components (`src/components/`)

**Modular component organization by functionality:**

- **`core/`** - Main PDF viewing and rendering components
- **`annotations/`** - Annotation functionality (overlays, export, history)
- **`forms/`** - PDF form handling (designer, manager, validation)
- **`search/`** - Search implementations (simple, enhanced, unified)
- **`navigation/`** - UI navigation (sidebar, bookmarks, thumbnails, toolbars)
- **`tools/`** - Utility features (OCR, image extraction, signatures, performance)
- **`workflow/`** - Workflow and version control components
- **`accessibility/`** - Accessibility enhancements
- **`collaboration/`** - Real-time collaboration features
- **`ui/`** - Reusable UI primitives (shadcn/ui components)

**Key files:**
- `index.ts` - Centralized component exports
- `theme-provider.tsx` - Theme context provider
- `README.md` - Component organization documentation

### Utilities (`src/lib/`)
- `utils.ts` - Shared utility functions (likely includes cn() for class merging)

## Import Patterns

### Path Aliases (configured in tsconfig.json)
```typescript
@/*           # Maps to ./src/*
@/components  # Maps to ./src/components
@/lib         # Maps to ./src/lib
```

### Component Import Strategies
```typescript
// Specific module imports (recommended)
import { PDFViewer } from '@/components/core'
import { PDFAnnotations } from '@/components/annotations'

// Main index imports (for commonly used components)
import { Button, Dialog } from '@/components'
```

## Configuration Files

- **`components.json`** - shadcn/ui configuration with aliases
- **`tsconfig.json`** - TypeScript configuration with path mapping
- **`eslint.config.mjs`** - ESLint flat configuration
- **`next.config.ts`** - Next.js configuration (minimal)
- **`postcss.config.mjs`** - PostCSS configuration for Tailwind
- **`package.json`** - Dependencies and scripts

## Naming Conventions

- **Components**: PascalCase with descriptive prefixes (`PDFViewer`, `PDFAnnotations`)
- **Files**: kebab-case for component files (`pdf-viewer.tsx`)
- **Folders**: lowercase with hyphens where needed
- **Exports**: Centralized through index files for clean imports

## Development Guidelines

- Components are organized by feature/functionality, not by type
- Each major feature area has its own folder with related components
- UI primitives are separated in the `ui/` folder
- Maintain backward compatibility through centralized exports
- Use TypeScript strict mode throughout the codebase
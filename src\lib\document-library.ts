"use client";

import type {
  DocumentInstance,
  DocumentMetadata,
  DocumentLibrary,
  DocumentCollection,
  DocumentLibrarySettings,
  DocumentSearchQuery,
  DocumentSearchResult,
  DocumentImportOptions,
  DocumentExportOptions
} from './types/pdf';
import { createDefaultDocumentMetadata, extractPDFMetadata, formatFileSize } from './types/pdf';

// IndexedDB database name and version
const DB_NAME = 'PDFDocumentLibrary';
const DB_VERSION = 1;
const DOCUMENTS_STORE = 'documents';
const COLLECTIONS_STORE = 'collections';
const SETTINGS_STORE = 'settings';
const FILES_STORE = 'files';

// Default library settings
const DEFAULT_LIBRARY_SETTINGS: DocumentLibrarySettings = {
  defaultView: 'grid',
  sortBy: 'dateAdded',
  sortOrder: 'desc',
  showThumbnails: true,
  thumbnailSize: 'medium',
  autoGenerateThumbnails: true,
  maxStorageSize: 500 * 1024 * 1024, // 500MB
  enableAutoBackup: true,
  backupInterval: 24 // hours
};

// Default collections
const DEFAULT_COLLECTIONS: Omit<DocumentCollection, 'id' | 'createdDate' | 'modifiedDate'>[] = [
  {
    name: 'Recent',
    description: 'Recently accessed documents',
    documentIds: [],
    color: '#3b82f6',
    icon: 'clock',
    isSystem: true
  },
  {
    name: 'Favorites',
    description: 'Favorite documents',
    documentIds: [],
    color: '#ef4444',
    icon: 'heart',
    isSystem: true
  },
  {
    name: 'Pinned',
    description: 'Pinned documents',
    documentIds: [],
    color: '#f59e0b',
    icon: 'pin',
    isSystem: true
  }
];

/**
 * Document Library Storage System
 * Manages document metadata, collections, and file storage using IndexedDB
 */
export class DocumentLibraryStorage {
  private db: IDBDatabase | null = null;
  private isInitialized = false;

  /**
   * Initialize the document library storage
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) return;

    return new Promise((resolve, reject) => {
      const request = indexedDB.open(DB_NAME, DB_VERSION);

      request.onerror = () => {
        reject(new Error('Failed to open IndexedDB'));
      };

      request.onsuccess = () => {
        this.db = request.result;
        this.isInitialized = true;
        resolve();
      };

      request.onupgradeneeded = (event) => {
        const db = (event.target as IDBOpenDBRequest).result;

        // Create documents store
        if (!db.objectStoreNames.contains(DOCUMENTS_STORE)) {
          const documentsStore = db.createObjectStore(DOCUMENTS_STORE, { keyPath: 'id' });
          documentsStore.createIndex('title', 'metadata.title', { unique: false });
          documentsStore.createIndex('author', 'metadata.author', { unique: false });
          documentsStore.createIndex('dateAdded', 'metadata.addedDate', { unique: false });
          documentsStore.createIndex('lastAccessed', 'metadata.lastAccessedDate', { unique: false });
          documentsStore.createIndex('tags', 'metadata.tags', { unique: false, multiEntry: true });
          documentsStore.createIndex('categories', 'metadata.categories', { unique: false, multiEntry: true });
          documentsStore.createIndex('isFavorite', 'metadata.isFavorite', { unique: false });
        }

        // Create collections store
        if (!db.objectStoreNames.contains(COLLECTIONS_STORE)) {
          const collectionsStore = db.createObjectStore(COLLECTIONS_STORE, { keyPath: 'id' });
          collectionsStore.createIndex('name', 'name', { unique: false });
          collectionsStore.createIndex('isSystem', 'isSystem', { unique: false });
        }

        // Create settings store
        if (!db.objectStoreNames.contains(SETTINGS_STORE)) {
          db.createObjectStore(SETTINGS_STORE, { keyPath: 'id' });
        }

        // Create files store for large file storage
        if (!db.objectStoreNames.contains(FILES_STORE)) {
          db.createObjectStore(FILES_STORE, { keyPath: 'id' });
        }
      };
    });
  }

  /**
   * Ensure the database is initialized
   */
  private async ensureInitialized(): Promise<void> {
    if (!this.isInitialized) {
      await this.initialize();
    }
  }

  /**
   * Add a document to the library
   */
  async addDocument(
    file: string | File,
    metadata?: Partial<DocumentMetadata>
  ): Promise<DocumentInstance> {
    await this.ensureInitialized();

    const documentId = `doc_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const defaultMetadata = createDefaultDocumentMetadata(file);
    const finalMetadata: DocumentMetadata = { ...defaultMetadata, ...metadata };

    const document: DocumentInstance = {
      id: documentId,
      file,
      title: finalMetadata.title,
      isLoading: false,
      hasError: false,
      numPages: finalMetadata.pageCount,
      lastAccessed: Date.now(),
      metadata: finalMetadata,
      pageNumber: 1,
      scale: 1.0,
      rotation: 0,
      searchText: '',
      bookmarks: [],
      annotations: [],
      formData: {}
    };

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction([DOCUMENTS_STORE], 'readwrite');
      const store = transaction.objectStore(DOCUMENTS_STORE);
      const request = store.add(document);

      request.onsuccess = () => {
        resolve(document);
      };

      request.onerror = () => {
        reject(new Error('Failed to add document to library'));
      };
    });
  }

  /**
   * Get a document by ID
   */
  async getDocument(documentId: string): Promise<DocumentInstance | null> {
    await this.ensureInitialized();

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction([DOCUMENTS_STORE], 'readonly');
      const store = transaction.objectStore(DOCUMENTS_STORE);
      const request = store.get(documentId);

      request.onsuccess = () => {
        resolve(request.result || null);
      };

      request.onerror = () => {
        reject(new Error('Failed to get document from library'));
      };
    });
  }

  /**
   * Update a document in the library
   */
  async updateDocument(documentId: string, updates: Partial<DocumentInstance>): Promise<void> {
    await this.ensureInitialized();

    const document = await this.getDocument(documentId);
    if (!document) {
      throw new Error('Document not found');
    }

    const updatedDocument = { ...document, ...updates };
    updatedDocument.metadata.lastAccessedDate = new Date();

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction([DOCUMENTS_STORE], 'readwrite');
      const store = transaction.objectStore(DOCUMENTS_STORE);
      const request = store.put(updatedDocument);

      request.onsuccess = () => {
        resolve();
      };

      request.onerror = () => {
        reject(new Error('Failed to update document in library'));
      };
    });
  }

  /**
   * Remove a document from the library
   */
  async removeDocument(documentId: string): Promise<void> {
    await this.ensureInitialized();

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction([DOCUMENTS_STORE], 'readwrite');
      const store = transaction.objectStore(DOCUMENTS_STORE);
      const request = store.delete(documentId);

      request.onsuccess = () => {
        resolve();
      };

      request.onerror = () => {
        reject(new Error('Failed to remove document from library'));
      };
    });
  }

  /**
   * Get all documents from the library
   */
  async getAllDocuments(): Promise<DocumentInstance[]> {
    await this.ensureInitialized();

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction([DOCUMENTS_STORE], 'readonly');
      const store = transaction.objectStore(DOCUMENTS_STORE);
      const request = store.getAll();

      request.onsuccess = () => {
        resolve(request.result || []);
      };

      request.onerror = () => {
        reject(new Error('Failed to get documents from library'));
      };
    });
  }

  /**
   * Search documents based on query
   */
  async searchDocuments(query: DocumentSearchQuery): Promise<DocumentSearchResult> {
    await this.ensureInitialized();

    const allDocuments = await this.getAllDocuments();
    let filteredDocuments = allDocuments;

    // Text search
    if (query.text) {
      const searchText = query.text.toLowerCase();
      filteredDocuments = filteredDocuments.filter(doc =>
        doc.metadata.title.toLowerCase().includes(searchText) ||
        doc.metadata.author?.toLowerCase().includes(searchText) ||
        doc.metadata.subject?.toLowerCase().includes(searchText) ||
        doc.metadata.description?.toLowerCase().includes(searchText) ||
        doc.metadata.keywords?.some(keyword => keyword.toLowerCase().includes(searchText))
      );
    }

    // Tags filter
    if (query.tags && query.tags.length > 0) {
      filteredDocuments = filteredDocuments.filter(doc =>
        query.tags!.some(tag => doc.metadata.tags.includes(tag))
      );
    }

    // Categories filter
    if (query.categories && query.categories.length > 0) {
      filteredDocuments = filteredDocuments.filter(doc =>
        query.categories!.some(category => doc.metadata.categories.includes(category))
      );
    }

    // Date range filter
    if (query.dateRange) {
      filteredDocuments = filteredDocuments.filter(doc => {
        const docDate = doc.metadata.addedDate;
        return docDate >= query.dateRange!.start && docDate <= query.dateRange!.end;
      });
    }

    // Size range filter
    if (query.sizeRange) {
      filteredDocuments = filteredDocuments.filter(doc => {
        const size = doc.metadata.fileSize;
        return size >= query.sizeRange!.min && size <= query.sizeRange!.max;
      });
    }

    // Page count filter
    if (query.pageCountRange) {
      filteredDocuments = filteredDocuments.filter(doc => {
        const pageCount = doc.metadata.pageCount;
        return pageCount >= query.pageCountRange!.min && pageCount <= query.pageCountRange!.max;
      });
    }

    // Boolean filters
    if (query.hasAnnotations !== undefined) {
      filteredDocuments = filteredDocuments.filter(doc =>
        doc.metadata.hasAnnotations === query.hasAnnotations
      );
    }

    if (query.hasBookmarks !== undefined) {
      filteredDocuments = filteredDocuments.filter(doc =>
        doc.metadata.hasBookmarks === query.hasBookmarks
      );
    }

    if (query.hasFormFields !== undefined) {
      filteredDocuments = filteredDocuments.filter(doc =>
        doc.metadata.hasFormFields === query.hasFormFields
      );
    }

    if (query.isFavorite !== undefined) {
      filteredDocuments = filteredDocuments.filter(doc =>
        doc.metadata.isFavorite === query.isFavorite
      );
    }

    if (query.rating !== undefined) {
      filteredDocuments = filteredDocuments.filter(doc =>
        doc.metadata.rating === query.rating
      );
    }

    // Generate facets
    const facets = this.generateFacets(allDocuments);

    return {
      documents: filteredDocuments,
      totalCount: filteredDocuments.length,
      facets
    };
  }

  /**
   * Generate search facets from documents
   */
  private generateFacets(documents: DocumentInstance[]) {
    const tagCounts = new Map<string, number>();
    const categoryCounts = new Map<string, number>();
    const collectionCounts = new Map<string, number>();
    const authorCounts = new Map<string, number>();

    documents.forEach(doc => {
      // Count tags
      doc.metadata.tags.forEach(tag => {
        tagCounts.set(tag, (tagCounts.get(tag) || 0) + 1);
      });

      // Count categories
      doc.metadata.categories.forEach(category => {
        categoryCounts.set(category, (categoryCounts.get(category) || 0) + 1);
      });

      // Count collections
      doc.metadata.collections.forEach(collection => {
        collectionCounts.set(collection, (collectionCounts.get(collection) || 0) + 1);
      });

      // Count authors
      if (doc.metadata.author) {
        authorCounts.set(doc.metadata.author, (authorCounts.get(doc.metadata.author) || 0) + 1);
      }
    });

    return {
      tags: Array.from(tagCounts.entries()).map(([name, count]) => ({ name, count })),
      categories: Array.from(categoryCounts.entries()).map(([name, count]) => ({ name, count })),
      collections: Array.from(collectionCounts.entries()).map(([name, count]) => ({ name, count })),
      authors: Array.from(authorCounts.entries()).map(([name, count]) => ({ name, count }))
    };
  }

  /**
   * Get recent documents
   */
  async getRecentDocuments(limit: number = 10): Promise<DocumentInstance[]> {
    const allDocuments = await this.getAllDocuments();
    return allDocuments
      .sort((a, b) => b.metadata.lastAccessedDate.getTime() - a.metadata.lastAccessedDate.getTime())
      .slice(0, limit);
  }

  /**
   * Get favorite documents
   */
  async getFavoriteDocuments(): Promise<DocumentInstance[]> {
    const allDocuments = await this.getAllDocuments();
    return allDocuments.filter(doc => doc.metadata.isFavorite);
  }

  /**
   * Get pinned documents
   */
  async getPinnedDocuments(): Promise<DocumentInstance[]> {
    const allDocuments = await this.getAllDocuments();
    return allDocuments.filter(doc => doc.metadata.isPinned);
  }

  /**
   * Collection Management Methods
   */

  /**
   * Create a new collection
   */
  async createCollection(
    name: string,
    description?: string,
    color?: string,
    icon?: string
  ): Promise<DocumentCollection> {
    await this.ensureInitialized();

    const collection: DocumentCollection = {
      id: `col_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      name,
      description,
      documentIds: [],
      color: color || '#6b7280',
      icon: icon || 'folder',
      isSystem: false,
      createdDate: new Date(),
      modifiedDate: new Date()
    };

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction([COLLECTIONS_STORE], 'readwrite');
      const store = transaction.objectStore(COLLECTIONS_STORE);
      const request = store.add(collection);

      request.onsuccess = () => {
        resolve(collection);
      };

      request.onerror = () => {
        reject(new Error('Failed to create collection'));
      };
    });
  }

  /**
   * Get all collections
   */
  async getAllCollections(): Promise<DocumentCollection[]> {
    await this.ensureInitialized();

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction([COLLECTIONS_STORE], 'readonly');
      const store = transaction.objectStore(COLLECTIONS_STORE);
      const request = store.getAll();

      request.onsuccess = () => {
        resolve(request.result || []);
      };

      request.onerror = () => {
        reject(new Error('Failed to get collections'));
      };
    });
  }

  /**
   * Add document to collection
   */
  async addDocumentToCollection(documentId: string, collectionId: string): Promise<void> {
    await this.ensureInitialized();

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction([COLLECTIONS_STORE], 'readwrite');
      const store = transaction.objectStore(COLLECTIONS_STORE);
      const request = store.get(collectionId);

      request.onsuccess = () => {
        const collection = request.result;
        if (!collection) {
          reject(new Error('Collection not found'));
          return;
        }

        if (!collection.documentIds.includes(documentId)) {
          collection.documentIds.push(documentId);
          collection.modifiedDate = new Date();

          const updateRequest = store.put(collection);
          updateRequest.onsuccess = () => resolve();
          updateRequest.onerror = () => reject(new Error('Failed to update collection'));
        } else {
          resolve(); // Document already in collection
        }
      };

      request.onerror = () => {
        reject(new Error('Failed to get collection'));
      };
    });
  }

  /**
   * Remove document from collection
   */
  async removeDocumentFromCollection(documentId: string, collectionId: string): Promise<void> {
    await this.ensureInitialized();

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction([COLLECTIONS_STORE], 'readwrite');
      const store = transaction.objectStore(COLLECTIONS_STORE);
      const request = store.get(collectionId);

      request.onsuccess = () => {
        const collection = request.result;
        if (!collection) {
          reject(new Error('Collection not found'));
          return;
        }

        const index = collection.documentIds.indexOf(documentId);
        if (index > -1) {
          collection.documentIds.splice(index, 1);
          collection.modifiedDate = new Date();

          const updateRequest = store.put(collection);
          updateRequest.onsuccess = () => resolve();
          updateRequest.onerror = () => reject(new Error('Failed to update collection'));
        } else {
          resolve(); // Document not in collection
        }
      };

      request.onerror = () => {
        reject(new Error('Failed to get collection'));
      };
    });
  }

  /**
   * Get documents in a collection
   */
  async getDocumentsInCollection(collectionId: string): Promise<DocumentInstance[]> {
    await this.ensureInitialized();

    const collections = await this.getAllCollections();
    const collection = collections.find(c => c.id === collectionId);

    if (!collection) {
      throw new Error('Collection not found');
    }

    const allDocuments = await this.getAllDocuments();
    return allDocuments.filter(doc => collection.documentIds.includes(doc.id));
  }

  /**
   * Initialize default collections if they don't exist
   */
  async initializeDefaultCollections(): Promise<void> {
    await this.ensureInitialized();

    const existingCollections = await this.getAllCollections();
    const systemCollections = existingCollections.filter(c => c.isSystem);

    for (const defaultCol of DEFAULT_COLLECTIONS) {
      const exists = systemCollections.some(c => c.name === defaultCol.name);
      if (!exists) {
        await this.createCollection(
          defaultCol.name,
          defaultCol.description,
          defaultCol.color,
          defaultCol.icon
        );
      }
    }
  }

  /**
   * Settings Management Methods
   */

  /**
   * Get library settings
   */
  async getSettings(): Promise<DocumentLibrarySettings> {
    await this.ensureInitialized();

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction([SETTINGS_STORE], 'readonly');
      const store = transaction.objectStore(SETTINGS_STORE);
      const request = store.get('library-settings');

      request.onsuccess = () => {
        resolve(request.result?.settings || DEFAULT_LIBRARY_SETTINGS);
      };

      request.onerror = () => {
        reject(new Error('Failed to get settings'));
      };
    });
  }

  /**
   * Update library settings
   */
  async updateSettings(settings: Partial<DocumentLibrarySettings>): Promise<void> {
    await this.ensureInitialized();

    const currentSettings = await this.getSettings();
    const updatedSettings = { ...currentSettings, ...settings };

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction([SETTINGS_STORE], 'readwrite');
      const store = transaction.objectStore(SETTINGS_STORE);
      const request = store.put({ id: 'library-settings', settings: updatedSettings });

      request.onsuccess = () => {
        resolve();
      };

      request.onerror = () => {
        reject(new Error('Failed to update settings'));
      };
    });
  }
}

// Export singleton instance
export const documentLibrary = new DocumentLibraryStorage();

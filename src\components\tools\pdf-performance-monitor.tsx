"use client";

import { useState, useEffect, useCallback } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { ScrollArea } from "@/components/ui/scroll-area";

export interface PerformanceMetrics {
  cpu: number;
  memory: number;
  renderTime: number;
  pageLoadTime: number;
  scriptingTime: number;
  paintingTime: number;
  layoutTime: number;
  networkTime: number;
  totalSize: number;
  cacheHitRate: number;
  fps: number;
  jankScore: number;
}

const initialMetrics: PerformanceMetrics = {
  cpu: 0,
  memory: 0,
  renderTime: 0,
  pageLoadTime: 0,
  scriptingTime: 0,
  paintingTime: 0,
  layoutTime: 0,
  networkTime: 0,
  totalSize: 0,
  cacheHitRate: 0,
  fps: 60,
  jankScore: 0,
};

interface PDFPerformanceMonitorProps {
  pdfDocument?: unknown;
  numPages?: number;
  currentPage?: number;
  onMetricsUpdate?: (metrics: PerformanceMetrics) => void;
  className?: string;
  // Enhanced configuration options
  monitoringInterval?: number;
  enableRealTimeUpdates?: boolean;
  enableHistoryTracking?: boolean;
  maxHistoryEntries?: number;
  alertThresholds?: {
    cpu?: number;
    memory?: number;
    fps?: number;
    jankScore?: number;
  };
  autoStart?: boolean;
}

const PdfPerformanceMonitor = ({
  onMetricsUpdate,
  monitoringInterval = 1000,
  enableHistoryTracking = true,
  maxHistoryEntries = 30,
  autoStart = false,
}: PDFPerformanceMonitorProps = {}) => {
  const [isMonitoring, setIsMonitoring] = useState(false);
  const [performanceMetrics, setPerformanceMetrics] = useState(initialMetrics);
  const [performanceScore, setPerformanceScore] = useState(100);
  const [history, setHistory] = useState<PerformanceMetrics[]>([]);

  const startMonitoring = () => {
    setIsMonitoring(true);
    // Simulate performance monitoring
  };

  const stopMonitoring = () => {
    setIsMonitoring(false);
  };

  const resetMetrics = () => {
    setPerformanceMetrics(initialMetrics);
    setPerformanceScore(100);
  };

  const updateMetrics = useCallback(() => {
    if (isMonitoring) {
      const newMetrics: PerformanceMetrics = {
        cpu: Math.random() * 100,
        memory: Math.random() * 100,
        renderTime: Math.random() * 50,
        pageLoadTime: Math.random() * 1000,
        scriptingTime: Math.random() * 100,
        paintingTime: Math.random() * 50,
        layoutTime: Math.random() * 30,
        networkTime: Math.random() * 200,
        totalSize: Math.random() * 10000000,
        cacheHitRate: Math.random() * 100,
        fps: 50 + Math.random() * 20,
        jankScore: Math.random() * 10
      };

      setPerformanceMetrics(newMetrics);
      if (enableHistoryTracking) {
        setHistory(prev => [...prev.slice(-(maxHistoryEntries - 1)), newMetrics]);
      }

      // Calculate performance score based on key metrics
      const score = 100 - (
        (newMetrics.cpu * 0.3) +
        (newMetrics.memory * 0.2) +
        (newMetrics.jankScore * 0.5)
      ) / 3;
      setPerformanceScore(Math.max(0, Math.min(100, score)));

      // Enhanced callback for compatibility
      onMetricsUpdate?.(newMetrics);
    }
  }, [isMonitoring, enableHistoryTracking, maxHistoryEntries, onMetricsUpdate]);

  useEffect(() => {
    let intervalId: NodeJS.Timeout;
    if (isMonitoring) {
      intervalId = setInterval(updateMetrics, monitoringInterval);
    }
    return () => clearInterval(intervalId);
  }, [isMonitoring, updateMetrics, monitoringInterval, enableHistoryTracking, maxHistoryEntries, onMetricsUpdate]);

  // Auto-start monitoring if enabled
  useEffect(() => {
    if (autoStart) {
      setIsMonitoring(true);
    }
  }, [autoStart]);

  const getScoreBadgeVariant = (score: number) => {
    if (score > 75) return "secondary";
    if (score > 50) return "outline";
    return "destructive";
  };

  return (
    <div className="h-full flex flex-col">
      <div className="p-4 border-b flex-shrink-0">
        <div className="flex items-center justify-between mb-4">
          <h3 className="font-medium">Performance Monitor</h3>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={isMonitoring ? stopMonitoring : startMonitoring}
            >
              {isMonitoring ? "Stop" : "Start"}
            </Button>
            <Button variant="outline" size="sm" onClick={resetMetrics}>
              Reset
            </Button>
          </div>
        </div>

        {/* Performance Score */}
        <div className="mb-4">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium">Performance Score</span>
            <Badge variant={getScoreBadgeVariant(performanceScore)}>
              {performanceScore}/100
            </Badge>
          </div>
          <Progress value={performanceScore} className="h-2" />
        </div>
      </div>

      <ScrollArea className="flex-1">
        <div className="p-4">
          <Tabs defaultValue="overview" className="w-full">
            <TabsList>
              <TabsTrigger value="overview">Overview</TabsTrigger>
              <TabsTrigger value="details">Details</TabsTrigger>
              <TabsTrigger value="history">History</TabsTrigger>
            </TabsList>
            <TabsContent value="overview">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <h4 className="font-medium mb-2">Resource Usage</h4>
                  <p className="text-sm">
                    CPU: {performanceMetrics.cpu.toFixed(2)}%
                  </p>
                  <p className="text-sm">
                    Memory: {performanceMetrics.memory.toFixed(2)}%
                  </p>
                  <p className="text-sm">
                    FPS: {performanceMetrics.fps.toFixed(2)}
                  </p>
                  <p className="text-sm">
                    Cache Hit Rate: {performanceMetrics.cacheHitRate.toFixed(2)}%
                  </p>
                </div>
                <div>
                  <h4 className="font-medium mb-2">Overall Performance</h4>
                  <p className="text-sm">
                    Performance Score: {performanceScore.toFixed(2)}/100
                  </p>
                  <p className="text-sm">
                    Jank Score: {performanceMetrics.jankScore.toFixed(2)}
                  </p>
                </div>
              </div>
            </TabsContent>
            <TabsContent value="details">
              <h4 className="font-medium mb-2">Detailed Metrics</h4>
              <p className="text-sm">
                Render Time: {performanceMetrics.renderTime.toFixed(2)} ms
              </p>
              <p className="text-sm">
                Page Load Time: {performanceMetrics.pageLoadTime.toFixed(2)} ms
              </p>
              <p className="text-sm">
                Scripting Time: {performanceMetrics.scriptingTime.toFixed(2)} ms
              </p>
              <p className="text-sm">
                Painting Time: {performanceMetrics.paintingTime.toFixed(2)} ms
              </p>
              <p className="text-sm">
                Layout Time: {performanceMetrics.layoutTime.toFixed(2)} ms
              </p>
              <p className="text-sm">
                Network Time: {performanceMetrics.networkTime.toFixed(2)} ms
              </p>
              <p className="text-sm">
                Total Size: {(performanceMetrics.totalSize / 1024 / 1024).toFixed(2)} MB
              </p>
            </TabsContent>
            <TabsContent value="history">
              <div className="space-y-4">
                <h4 className="font-medium">Performance History</h4>
                {history.length > 0 ? (
                  <div className="space-y-2">
                    <div className="h-16 flex items-end space-x-1">
                      {history.slice(-20).map((entry, index) => {
                        const score = 100 - ((entry.cpu * 0.3) + (entry.memory * 0.2) + (entry.jankScore * 0.5)) / 3;
                        const height = Math.max(4, (score / 100) * 60);
                        const color = score > 80 ? "bg-green-500" : score > 60 ? "bg-yellow-500" : "bg-red-500";
                        return (
                          <div
                            key={index}
                            className={`w-2 rounded-t ${color}`}
                            style={{ height: `${height}px` }}
                            title={`Score: ${score.toFixed(1)}`}
                          />
                        );
                      })}
                    </div>
                    <div className="text-xs text-muted-foreground text-center">
                      Last {Math.min(20, history.length)} measurements
                    </div>
                  </div>
                ) : (
                  <p className="text-sm text-muted-foreground">
                    Start monitoring to see performance history
                  </p>
                )}
              </div>
            </TabsContent>
          </Tabs>
        </div>
      </ScrollArea>
    </div>
  );
};

export default PdfPerformanceMonitor;

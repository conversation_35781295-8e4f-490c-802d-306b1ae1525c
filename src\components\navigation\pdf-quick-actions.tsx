"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  Search,
  Bookmark,
  PenTool,
  FileText,
  History,
  Users,
  Download,
  Printer,
  Grid3X3,
  List,
} from "lucide-react";

interface PDFQuickActionsProps {
  onAction: (action: string) => void;
  annotationCount: number;
  bookmarkCount: number;
  formFieldCount: number;
  isCollaborating: boolean;
}

export default function PDFQuickActions({
  onAction,
  annotationCount,
  bookmarkCount,
  formFieldCount,
  isCollaborating,
}: PDFQuickActionsProps) {
  const quickActions = [
    {
      id: "search",
      label: "Search",
      icon: Search,
      description: "Find text in document",
      shortcut: "Ctrl+F",
      color: "blue",
    },
    {
      id: "annotate",
      label: "Annotate",
      icon: PenTool,
      description: "Add annotations",
      shortcut: "Ctrl+3",
      color: "green",
      badge: annotationCount > 0 ? annotationCount : null,
    },
    {
      id: "bookmarks",
      label: "Bookmarks",
      icon: Bookmark,
      description: "Manage bookmarks",
      shortcut: "Ctrl+B",
      color: "yellow",
      badge: bookmarkCount > 0 ? bookmarkCount : null,
    },
    {
      id: "forms",
      label: "Forms",
      icon: FileText,
      description: "Fill forms",
      shortcut: "Ctrl+4",
      color: "purple",
      badge: formFieldCount > 0 ? formFieldCount : null,
    },
    {
      id: "versions",
      label: "Versions",
      icon: History,
      description: "Version history",
      shortcut: "Ctrl+H",
      color: "indigo",
    },
    {
      id: "collaborate",
      label: "Share",
      icon: Users,
      description: "Collaborate",
      shortcut: "Ctrl+Shift+S",
      color: "pink",
      badge: isCollaborating ? "Live" : null,
    },
  ];

  const utilityActions = [
    {
      id: "outline",
      label: "Outline",
      icon: List,
      shortcut: "Ctrl+1",
    },
    {
      id: "thumbnails",
      label: "Thumbnails",
      icon: Grid3X3,
      shortcut: "Ctrl+T",
    },
    {
      id: "export",
      label: "Export",
      icon: Download,
      shortcut: "Ctrl+E",
    },
    {
      id: "print",
      label: "Print",
      icon: Printer,
      shortcut: "Ctrl+P",
    },
  ];

  return (
    <Card className="p-4 space-y-4">
      <div>
        <h3 className="font-medium text-sm mb-3">Quick Actions</h3>
        <div className="grid grid-cols-2 gap-2">
          {quickActions.map((action) => {
            const Icon = action.icon;
            return (
              <Button
                key={action.id}
                variant="outline"
                size="sm"
                onClick={() => onAction(action.id)}
                className="flex items-center gap-2 h-auto p-3 justify-start"
              >
                <div className="flex items-center gap-2 flex-1">
                  <Icon className="h-4 w-4" />
                  <div className="text-left">
                    <div className="font-medium text-xs">{action.label}</div>
                    <div className="text-xs text-muted-foreground">
                      {action.shortcut}
                    </div>
                  </div>
                </div>
                {action.badge && (
                  <Badge variant="secondary" className="text-xs">
                    {action.badge}
                  </Badge>
                )}
              </Button>
            );
          })}
        </div>
      </div>

      <div>
        <h3 className="font-medium text-sm mb-3">Tools</h3>
        <div className="grid grid-cols-4 gap-1">
          {utilityActions.map((action) => {
            const Icon = action.icon;
            return (
              <Button
                key={action.id}
                variant="ghost"
                size="sm"
                onClick={() => onAction(action.id)}
                className="flex flex-col items-center gap-1 h-auto p-2"
                title={`${action.label} (${action.shortcut})`}
              >
                <Icon className="h-4 w-4" />
                <span className="text-xs">{action.label}</span>
              </Button>
            );
          })}
        </div>
      </div>

      <div className="text-xs text-muted-foreground space-y-1">
        <div className="font-medium">Keyboard Shortcuts:</div>
        <div>• Ctrl+1-4: Quick navigation</div>
        <div>• Escape: Exit current mode</div>
        <div>• Arrow keys: Navigate pages</div>
        <div>• Ctrl+C: Copy selected text</div>
      </div>
    </Card>
  );
}

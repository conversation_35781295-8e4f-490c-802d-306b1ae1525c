"use client";

import React, { useState, useCallback, useRef } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import {
  Upload,
  Download,
  FileText,
  Folder,
  Link,
  Cloud,
  Archive,
  CheckCircle,
  AlertCircle,
  Loader2,
  X,
  Plus,
  Settings
} from 'lucide-react';
import { toast } from 'sonner';
import { cn } from '@/lib/utils';
import type {
  DocumentInstance,
  DocumentCollection,
  DocumentImportOptions,
  DocumentExportOptions
} from '@/lib/types/pdf';
import { documentLibrary } from '@/lib/document-library';
import { createDefaultDocumentMetadata } from '@/lib/types/pdf';

interface DocumentImportExportProps {
  documents: DocumentInstance[];
  collections: DocumentCollection[];
  onDocumentsUpdate: () => void;
  onCollectionsUpdate: () => void;
}

interface ImportProgress {
  total: number;
  completed: number;
  current: string;
  errors: string[];
  warnings: string[];
}

interface ExportProgress {
  total: number;
  completed: number;
  current: string;
}

export default function DocumentImportExport({
  documents,
  collections,
  onDocumentsUpdate,
  onCollectionsUpdate
}: DocumentImportExportProps) {
  const [showImportDialog, setShowImportDialog] = useState(false);
  const [showExportDialog, setShowExportDialog] = useState(false);
  const [importProgress, setImportProgress] = useState<ImportProgress | null>(null);
  const [exportProgress, setExportProgress] = useState<ExportProgress | null>(null);
  const [isImporting, setIsImporting] = useState(false);
  const [isExporting, setIsExporting] = useState(false);

  // Import options
  const [importOptions, setImportOptions] = useState<DocumentImportOptions>({
    source: 'file',
    extractMetadata: true,
    generateThumbnails: true,
    autoTagging: false,
    duplicateHandling: 'skip',
    defaultTags: [],
    defaultCollection: undefined
  });

  // Export options
  const [exportOptions, setExportOptions] = useState<DocumentExportOptions>({
    format: 'json',
    includeFiles: false,
    includeMetadata: true,
    includeThumbnails: false,
    compression: true,
    selectedDocuments: [],
    selectedCollections: []
  });

  const [urlList, setUrlList] = useState('');
  const [newTag, setNewTag] = useState('');
  const fileInputRef = useRef<HTMLInputElement>(null);
  const folderInputRef = useRef<HTMLInputElement>(null);

  // Import handlers
  const handleFileImport = useCallback(async (files: FileList) => {
    if (!files.length) return;

    setIsImporting(true);
    setImportProgress({
      total: files.length,
      completed: 0,
      current: '',
      errors: [],
      warnings: []
    });

    try {
      for (let i = 0; i < files.length; i++) {
        const file = files[i];
        
        setImportProgress(prev => prev ? {
          ...prev,
          current: file.name,
          completed: i
        } : null);

        try {
          // Validate file type
          if (file.type !== 'application/pdf') {
            setImportProgress(prev => prev ? {
              ...prev,
              warnings: [...prev.warnings, `Skipped ${file.name}: Not a PDF file`]
            } : null);
            continue;
          }

          // Check for duplicates
          const existingDoc = documents.find(doc => 
            typeof doc.file !== 'string' && 
            doc.file.name === file.name && 
            doc.file.size === file.size
          );

          if (existingDoc && importOptions.duplicateHandling === 'skip') {
            setImportProgress(prev => prev ? {
              ...prev,
              warnings: [...prev.warnings, `Skipped ${file.name}: Duplicate file`]
            } : null);
            continue;
          }

          // Create metadata
          const metadata = createDefaultDocumentMetadata(file);
          if (importOptions.defaultTags) {
            metadata.tags = [...metadata.tags, ...importOptions.defaultTags];
          }

          // Add to library
          const document = await documentLibrary.addDocument(file, metadata);

          // Add to default collection if specified
          if (importOptions.defaultCollection) {
            await documentLibrary.addDocumentToCollection(document.id, importOptions.defaultCollection);
          }

        } catch (error) {
          setImportProgress(prev => prev ? {
            ...prev,
            errors: [...prev.errors, `Failed to import ${file.name}: ${error instanceof Error ? error.message : 'Unknown error'}`]
          } : null);
        }
      }

      setImportProgress(prev => prev ? {
        ...prev,
        completed: files.length,
        current: 'Import completed'
      } : null);

      onDocumentsUpdate();
      onCollectionsUpdate();
      
      toast.success(`Import completed: ${files.length} files processed`);
    } catch (error) {
      toast.error('Import failed');
    } finally {
      setIsImporting(false);
    }
  }, [documents, importOptions, onDocumentsUpdate, onCollectionsUpdate]);

  const handleUrlImport = useCallback(async () => {
    const urls = urlList.split('\n').filter(url => url.trim());
    if (!urls.length) return;

    setIsImporting(true);
    setImportProgress({
      total: urls.length,
      completed: 0,
      current: '',
      errors: [],
      warnings: []
    });

    try {
      for (let i = 0; i < urls.length; i++) {
        const url = urls[i].trim();
        
        setImportProgress(prev => prev ? {
          ...prev,
          current: url,
          completed: i
        } : null);

        try {
          // Validate URL
          new URL(url);

          // Create metadata
          const metadata = createDefaultDocumentMetadata(url);
          if (importOptions.defaultTags) {
            metadata.tags = [...metadata.tags, ...importOptions.defaultTags];
          }

          // Add to library
          const document = await documentLibrary.addDocument(url, metadata);

          // Add to default collection if specified
          if (importOptions.defaultCollection) {
            await documentLibrary.addDocumentToCollection(document.id, importOptions.defaultCollection);
          }

        } catch (error) {
          setImportProgress(prev => prev ? {
            ...prev,
            errors: [...prev.errors, `Failed to import ${url}: ${error instanceof Error ? error.message : 'Invalid URL'}`]
          } : null);
        }
      }

      setImportProgress(prev => prev ? {
        ...prev,
        completed: urls.length,
        current: 'Import completed'
      } : null);

      onDocumentsUpdate();
      onCollectionsUpdate();
      
      toast.success(`URL import completed: ${urls.length} URLs processed`);
    } catch (error) {
      toast.error('URL import failed');
    } finally {
      setIsImporting(false);
    }
  }, [urlList, importOptions, onDocumentsUpdate, onCollectionsUpdate]);

  // Export handlers
  const handleExport = useCallback(async () => {
    setIsExporting(true);
    setExportProgress({
      total: 1,
      completed: 0,
      current: 'Preparing export...'
    });

    try {
      let exportData: any = {};

      // Get documents to export
      const docsToExport = exportOptions.selectedDocuments.length > 0
        ? documents.filter(doc => exportOptions.selectedDocuments.includes(doc.id))
        : documents;

      // Get collections to export
      const collectionsToExport = exportOptions.selectedCollections.length > 0
        ? collections.filter(col => exportOptions.selectedCollections.includes(col.id))
        : collections;

      setExportProgress(prev => prev ? {
        ...prev,
        current: 'Collecting document data...'
      } : null);

      // Prepare export data
      if (exportOptions.includeMetadata) {
        exportData.documents = docsToExport.map(doc => ({
          id: doc.id,
          title: doc.title,
          metadata: doc.metadata,
          bookmarks: doc.bookmarks,
          annotations: doc.annotations,
          formData: doc.formData
        }));

        exportData.collections = collectionsToExport;
      }

      if (exportOptions.includeFiles) {
        // Note: In a real implementation, you'd need to handle file data appropriately
        exportData.files = docsToExport.map(doc => ({
          id: doc.id,
          fileName: doc.metadata.fileName,
          // File data would be included here in a real implementation
        }));
      }

      exportData.exportInfo = {
        timestamp: new Date().toISOString(),
        version: '1.0',
        documentCount: docsToExport.length,
        collectionCount: collectionsToExport.length
      };

      setExportProgress(prev => prev ? {
        ...prev,
        current: 'Generating export file...'
      } : null);

      // Generate export file
      let exportContent: string;
      let fileName: string;
      let mimeType: string;

      switch (exportOptions.format) {
        case 'json':
          exportContent = JSON.stringify(exportData, null, 2);
          fileName = `document-library-export-${new Date().toISOString().split('T')[0]}.json`;
          mimeType = 'application/json';
          break;
        case 'csv':
          // Simple CSV export for documents
          const csvHeaders = ['Title', 'Author', 'File Size', 'Page Count', 'Tags', 'Categories', 'Date Added'];
          const csvRows = docsToExport.map(doc => [
            doc.metadata.title,
            doc.metadata.author || '',
            doc.metadata.fileSize.toString(),
            doc.metadata.pageCount.toString(),
            doc.metadata.tags.join(';'),
            doc.metadata.categories.join(';'),
            doc.metadata.addedDate.toISOString()
          ]);
          exportContent = [csvHeaders, ...csvRows].map(row => row.join(',')).join('\n');
          fileName = `document-library-export-${new Date().toISOString().split('T')[0]}.csv`;
          mimeType = 'text/csv';
          break;
        case 'xml':
          // Simple XML export
          exportContent = `<?xml version="1.0" encoding="UTF-8"?>
<documentLibrary>
  <exportInfo>
    <timestamp>${exportData.exportInfo.timestamp}</timestamp>
    <documentCount>${exportData.exportInfo.documentCount}</documentCount>
    <collectionCount>${exportData.exportInfo.collectionCount}</collectionCount>
  </exportInfo>
  <documents>
    ${docsToExport.map(doc => `
    <document id="${doc.id}">
      <title>${doc.metadata.title}</title>
      <author>${doc.metadata.author || ''}</author>
      <fileSize>${doc.metadata.fileSize}</fileSize>
      <pageCount>${doc.metadata.pageCount}</pageCount>
      <tags>${doc.metadata.tags.join(',')}</tags>
      <categories>${doc.metadata.categories.join(',')}</categories>
      <dateAdded>${doc.metadata.addedDate.toISOString()}</dateAdded>
    </document>`).join('')}
  </documents>
</documentLibrary>`;
          fileName = `document-library-export-${new Date().toISOString().split('T')[0]}.xml`;
          mimeType = 'application/xml';
          break;
        default:
          throw new Error('Unsupported export format');
      }

      setExportProgress(prev => prev ? {
        ...prev,
        current: 'Downloading file...',
        completed: 1
      } : null);

      // Download file
      const blob = new Blob([exportContent], { type: mimeType });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = fileName;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);

      toast.success('Export completed successfully');
    } catch (error) {
      toast.error('Export failed');
    } finally {
      setIsExporting(false);
      setExportProgress(null);
    }
  }, [documents, collections, exportOptions]);

  // Helper functions
  const addDefaultTag = () => {
    if (newTag.trim() && !importOptions.defaultTags?.includes(newTag.trim())) {
      setImportOptions(prev => ({
        ...prev,
        defaultTags: [...(prev.defaultTags || []), newTag.trim()]
      }));
      setNewTag('');
    }
  };

  const removeDefaultTag = (tag: string) => {
    setImportOptions(prev => ({
      ...prev,
      defaultTags: prev.defaultTags?.filter(t => t !== tag) || []
    }));
  };

  return (
    <div className="space-y-4">
      {/* Import Section */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Upload className="h-5 w-5" />
            Import Documents
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {/* File Import */}
            <Dialog open={showImportDialog} onOpenChange={setShowImportDialog}>
              <DialogTrigger asChild>
                <Button variant="outline" className="h-20 flex-col">
                  <FileText className="h-6 w-6 mb-2" />
                  Import Files
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-2xl">
                <DialogHeader>
                  <DialogTitle>Import Documents</DialogTitle>
                </DialogHeader>
                <div className="space-y-4">
                  {/* Import Options */}
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label>Import Source</Label>
                      <Select 
                        value={importOptions.source} 
                        onValueChange={(value: any) => setImportOptions(prev => ({ ...prev, source: value }))}
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="file">Individual Files</SelectItem>
                          <SelectItem value="folder">Folder</SelectItem>
                          <SelectItem value="url">URLs</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div>
                      <Label>Duplicate Handling</Label>
                      <Select 
                        value={importOptions.duplicateHandling} 
                        onValueChange={(value: any) => setImportOptions(prev => ({ ...prev, duplicateHandling: value }))}
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="skip">Skip Duplicates</SelectItem>
                          <SelectItem value="replace">Replace Existing</SelectItem>
                          <SelectItem value="rename">Rename New</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  {/* Import Settings */}
                  <div className="space-y-2">
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="extract-metadata"
                        checked={importOptions.extractMetadata}
                        onCheckedChange={(checked) => 
                          setImportOptions(prev => ({ ...prev, extractMetadata: !!checked }))
                        }
                      />
                      <Label htmlFor="extract-metadata">Extract PDF metadata</Label>
                    </div>

                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="generate-thumbnails"
                        checked={importOptions.generateThumbnails}
                        onCheckedChange={(checked) => 
                          setImportOptions(prev => ({ ...prev, generateThumbnails: !!checked }))
                        }
                      />
                      <Label htmlFor="generate-thumbnails">Generate thumbnails</Label>
                    </div>

                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="auto-tagging"
                        checked={importOptions.autoTagging}
                        onCheckedChange={(checked) => 
                          setImportOptions(prev => ({ ...prev, autoTagging: !!checked }))
                        }
                      />
                      <Label htmlFor="auto-tagging">Auto-generate tags</Label>
                    </div>
                  </div>

                  {/* Default Collection */}
                  <div>
                    <Label>Default Collection (Optional)</Label>
                    <Select 
                      value={importOptions.defaultCollection || ''} 
                      onValueChange={(value) => 
                        setImportOptions(prev => ({ ...prev, defaultCollection: value || undefined }))
                      }
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select collection..." />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="">No collection</SelectItem>
                        {collections.filter(c => !c.isSystem).map(collection => (
                          <SelectItem key={collection.id} value={collection.id}>
                            {collection.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  {/* Default Tags */}
                  <div>
                    <Label>Default Tags</Label>
                    <div className="flex gap-2 mt-1">
                      <Input
                        placeholder="Add a tag..."
                        value={newTag}
                        onChange={(e) => setNewTag(e.target.value)}
                        onKeyDown={(e) => {
                          if (e.key === 'Enter') {
                            e.preventDefault();
                            addDefaultTag();
                          }
                        }}
                        className="flex-1"
                      />
                      <Button type="button" onClick={addDefaultTag} size="sm">
                        <Plus className="h-4 w-4" />
                      </Button>
                    </div>
                    {importOptions.defaultTags && importOptions.defaultTags.length > 0 && (
                      <div className="flex flex-wrap gap-1 mt-2">
                        {importOptions.defaultTags.map(tag => (
                          <Badge key={tag} variant="secondary" className="flex items-center gap-1">
                            {tag}
                            <X
                              className="h-3 w-3 cursor-pointer"
                              onClick={() => removeDefaultTag(tag)}
                            />
                          </Badge>
                        ))}
                      </div>
                    )}
                  </div>

                  <Separator />

                  {/* Import Actions */}
                  {importOptions.source === 'file' && (
                    <div>
                      <input
                        ref={fileInputRef}
                        type="file"
                        accept=".pdf"
                        multiple
                        onChange={(e) => e.target.files && handleFileImport(e.target.files)}
                        className="hidden"
                      />
                      <Button 
                        onClick={() => fileInputRef.current?.click()}
                        disabled={isImporting}
                        className="w-full"
                      >
                        {isImporting ? (
                          <>
                            <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                            Importing...
                          </>
                        ) : (
                          <>
                            <Upload className="h-4 w-4 mr-2" />
                            Select PDF Files
                          </>
                        )}
                      </Button>
                    </div>
                  )}

                  {importOptions.source === 'folder' && (
                    <div>
                      <input
                        ref={folderInputRef}
                        type="file"
                        webkitdirectory=""
                        multiple
                        onChange={(e) => e.target.files && handleFileImport(e.target.files)}
                        className="hidden"
                      />
                      <Button 
                        onClick={() => folderInputRef.current?.click()}
                        disabled={isImporting}
                        className="w-full"
                      >
                        {isImporting ? (
                          <>
                            <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                            Importing...
                          </>
                        ) : (
                          <>
                            <Folder className="h-4 w-4 mr-2" />
                            Select Folder
                          </>
                        )}
                      </Button>
                    </div>
                  )}

                  {importOptions.source === 'url' && (
                    <div className="space-y-2">
                      <Label>PDF URLs (one per line)</Label>
                      <Textarea
                        placeholder="https://example.com/document1.pdf&#10;https://example.com/document2.pdf"
                        value={urlList}
                        onChange={(e) => setUrlList(e.target.value)}
                        rows={5}
                      />
                      <Button 
                        onClick={handleUrlImport}
                        disabled={isImporting || !urlList.trim()}
                        className="w-full"
                      >
                        {isImporting ? (
                          <>
                            <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                            Importing...
                          </>
                        ) : (
                          <>
                            <Link className="h-4 w-4 mr-2" />
                            Import from URLs
                          </>
                        )}
                      </Button>
                    </div>
                  )}

                  {/* Import Progress */}
                  {importProgress && (
                    <div className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span>Progress: {importProgress.completed}/{importProgress.total}</span>
                        <span>{Math.round((importProgress.completed / importProgress.total) * 100)}%</span>
                      </div>
                      <Progress value={(importProgress.completed / importProgress.total) * 100} />
                      <p className="text-sm text-muted-foreground">{importProgress.current}</p>
                      
                      {importProgress.errors.length > 0 && (
                        <div className="space-y-1">
                          <p className="text-sm font-medium text-red-600">Errors:</p>
                          {importProgress.errors.map((error, index) => (
                            <p key={index} className="text-xs text-red-600">• {error}</p>
                          ))}
                        </div>
                      )}
                      
                      {importProgress.warnings.length > 0 && (
                        <div className="space-y-1">
                          <p className="text-sm font-medium text-yellow-600">Warnings:</p>
                          {importProgress.warnings.map((warning, index) => (
                            <p key={index} className="text-xs text-yellow-600">• {warning}</p>
                          ))}
                        </div>
                      )}
                    </div>
                  )}
                </div>
              </DialogContent>
            </Dialog>

            {/* URL Import */}
            <Button variant="outline" className="h-20 flex-col" onClick={() => {
              setImportOptions(prev => ({ ...prev, source: 'url' }));
              setShowImportDialog(true);
            }}>
              <Link className="h-6 w-6 mb-2" />
              Import URLs
            </Button>

            {/* Cloud Import */}
            <Button variant="outline" className="h-20 flex-col" disabled>
              <Cloud className="h-6 w-6 mb-2" />
              Cloud Import
              <Badge variant="secondary" className="mt-1">Coming Soon</Badge>
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Export Section */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Download className="h-5 w-5" />
            Export Documents
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {/* Metadata Export */}
            <Dialog open={showExportDialog} onOpenChange={setShowExportDialog}>
              <DialogTrigger asChild>
                <Button variant="outline" className="h-20 flex-col">
                  <Archive className="h-6 w-6 mb-2" />
                  Export Library
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-2xl">
                <DialogHeader>
                  <DialogTitle>Export Document Library</DialogTitle>
                </DialogHeader>
                <div className="space-y-4">
                  {/* Export Format */}
                  <div>
                    <Label>Export Format</Label>
                    <Select 
                      value={exportOptions.format} 
                      onValueChange={(value: any) => setExportOptions(prev => ({ ...prev, format: value }))}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="json">JSON</SelectItem>
                        <SelectItem value="csv">CSV</SelectItem>
                        <SelectItem value="xml">XML</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  {/* Export Options */}
                  <div className="space-y-2">
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="include-metadata"
                        checked={exportOptions.includeMetadata}
                        onCheckedChange={(checked) => 
                          setExportOptions(prev => ({ ...prev, includeMetadata: !!checked }))
                        }
                      />
                      <Label htmlFor="include-metadata">Include metadata</Label>
                    </div>

                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="include-files"
                        checked={exportOptions.includeFiles}
                        onCheckedChange={(checked) => 
                          setExportOptions(prev => ({ ...prev, includeFiles: !!checked }))
                        }
                      />
                      <Label htmlFor="include-files">Include file data</Label>
                    </div>

                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="include-thumbnails"
                        checked={exportOptions.includeThumbnails}
                        onCheckedChange={(checked) => 
                          setExportOptions(prev => ({ ...prev, includeThumbnails: !!checked }))
                        }
                      />
                      <Label htmlFor="include-thumbnails">Include thumbnails</Label>
                    </div>

                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="compression"
                        checked={exportOptions.compression}
                        onCheckedChange={(checked) => 
                          setExportOptions(prev => ({ ...prev, compression: !!checked }))
                        }
                      />
                      <Label htmlFor="compression">Use compression</Label>
                    </div>
                  </div>

                  <Separator />

                  {/* Export Actions */}
                  <Button 
                    onClick={handleExport}
                    disabled={isExporting}
                    className="w-full"
                  >
                    {isExporting ? (
                      <>
                        <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                        Exporting...
                      </>
                    ) : (
                      <>
                        <Download className="h-4 w-4 mr-2" />
                        Export Library
                      </>
                    )}
                  </Button>

                  {/* Export Progress */}
                  {exportProgress && (
                    <div className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span>Progress: {exportProgress.completed}/{exportProgress.total}</span>
                        <span>{Math.round((exportProgress.completed / exportProgress.total) * 100)}%</span>
                      </div>
                      <Progress value={(exportProgress.completed / exportProgress.total) * 100} />
                      <p className="text-sm text-muted-foreground">{exportProgress.current}</p>
                    </div>
                  )}
                </div>
              </DialogContent>
            </Dialog>

            {/* Backup Export */}
            <Button variant="outline" className="h-20 flex-col" onClick={() => {
              setExportOptions(prev => ({ 
                ...prev, 
                format: 'json',
                includeMetadata: true,
                includeFiles: false,
                includeThumbnails: false
              }));
              setShowExportDialog(true);
            }}>
              <Archive className="h-6 w-6 mb-2" />
              Quick Backup
            </Button>

            {/* Cloud Export */}
            <Button variant="outline" className="h-20 flex-col" disabled>
              <Cloud className="h-6 w-6 mb-2" />
              Cloud Backup
              <Badge variant="secondary" className="mt-1">Coming Soon</Badge>
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { configurePDFWorker, testPDFWorker, logPDFLoadAttempt, logPDFLoadSuccess, handlePDFError } from '@/lib/pdf-worker-config'
import { pdfjs } from 'react-pdf'

// Mock pdfjs
vi.mock('react-pdf', () => ({
  pdfjs: {
    version: '3.11.174',
    GlobalWorkerOptions: {
      workerSrc: '',
      disableFontFace: false,
      verbosity: 0,
      maxImageSize: 0,
      disableAutoFetch: false,
      disableStream: false,
    },
    VerbosityLevel: {
      INFOS: 1,
      WARNINGS: 2,
      ERRORS: 5,
    },
    getDocument: vi.fn(),
  },
}))

describe('PDF Worker Configuration', () => {
  const originalEnv = process.env.NODE_ENV
  const originalWindow = global.window

  beforeEach(() => {
    vi.clearAllMocks()
    // Reset worker options
    pdfjs.GlobalWorkerOptions.workerSrc = ''
    pdfjs.GlobalWorkerOptions.disableFontFace = false
    pdfjs.GlobalWorkerOptions.verbosity = 0
    pdfjs.GlobalWorkerOptions.maxImageSize = 0
    pdfjs.GlobalWorkerOptions.disableAutoFetch = false
    pdfjs.GlobalWorkerOptions.disableStream = false
  })

  afterEach(() => {
    process.env.NODE_ENV = originalEnv
    global.window = originalWindow
  })

  describe('configurePDFWorker', () => {
    it('configures worker for production environment', () => {
      process.env.NODE_ENV = 'production'
      global.window = {} as any

      configurePDFWorker()

      expect(pdfjs.GlobalWorkerOptions.workerSrc).toBe('/pdf.worker.min.js')
      expect(pdfjs.GlobalWorkerOptions.disableFontFace).toBe(false)
      expect(pdfjs.GlobalWorkerOptions.maxImageSize).toBe(1024 * 1024 * 50)
      expect(pdfjs.GlobalWorkerOptions.disableAutoFetch).toBe(false)
      expect(pdfjs.GlobalWorkerOptions.disableStream).toBe(false)
    })

    it('configures worker for development environment', () => {
      process.env.NODE_ENV = 'development'
      global.window = {} as any

      configurePDFWorker()

      expect(pdfjs.GlobalWorkerOptions.workerSrc).toBe(`//unpkg.com/pdfjs-dist@${pdfjs.version}/build/pdf.worker.min.js`)
      expect(pdfjs.GlobalWorkerOptions.verbosity).toBe(pdfjs.VerbosityLevel.INFOS)
    })

    it('handles server-side rendering gracefully', () => {
      process.env.NODE_ENV = 'production'
      global.window = undefined as any

      expect(() => configurePDFWorker()).not.toThrow()
      expect(pdfjs.GlobalWorkerOptions.workerSrc).toBe('/pdf.worker.min.js')
    })
  })

  describe('testPDFWorker', () => {
    it('returns true when worker loads successfully', async () => {
      const mockPdf = { numPages: 1 }
      const mockLoadingTask = { promise: Promise.resolve(mockPdf) }
      vi.mocked(pdfjs.getDocument).mockReturnValue(mockLoadingTask as any)

      const result = await testPDFWorker()

      expect(result).toBe(true)
      expect(pdfjs.getDocument).toHaveBeenCalledWith({ data: expect.any(Uint8Array) })
    })

    it('returns false when worker fails to load', async () => {
      const mockLoadingTask = { promise: Promise.reject(new Error('Worker failed')) }
      vi.mocked(pdfjs.getDocument).mockReturnValue(mockLoadingTask as any)

      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {})

      const result = await testPDFWorker()

      expect(result).toBe(false)
      expect(consoleSpy).toHaveBeenCalledWith('PDF.js worker test failed:', expect.any(Error))

      consoleSpy.mockRestore()
    })

    it('returns false when PDF has wrong number of pages', async () => {
      const mockPdf = { numPages: 2 } // Expected 1 page
      const mockLoadingTask = { promise: Promise.resolve(mockPdf) }
      vi.mocked(pdfjs.getDocument).mockReturnValue(mockLoadingTask as any)

      const result = await testPDFWorker()

      expect(result).toBe(false)
    })
  })

  describe('logPDFLoadAttempt', () => {
    beforeEach(() => {
      // Mock browser APIs
      global.navigator = {
        userAgent: 'test-agent',
      } as any
      global.window = {
        innerWidth: 1920,
        innerHeight: 1080,
      } as any
      global.performance = {
        memory: {
          usedJSHeapSize: 1024 * 1024 * 10,
          totalJSHeapSize: 1024 * 1024 * 20,
          jsHeapSizeLimit: 1024 * 1024 * 100,
        }
      } as any
    })

    it('logs file object information', () => {
      const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {})
      const file = new File(['test'], 'test.pdf', { type: 'application/pdf' })

      logPDFLoadAttempt(file, 'test-context')

      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringContaining('[PDF Load Attempt]'),
        expect.objectContaining({
          context: 'test-context',
          file: expect.objectContaining({
            type: 'file',
            name: 'test.pdf',
            size: expect.any(Number),
          }),
        })
      )

      consoleSpy.mockRestore()
    })

    it('logs URL string information', () => {
      const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {})
      const url = 'https://example.com/test.pdf'

      logPDFLoadAttempt(url, 'test-context')

      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringContaining('[PDF Load Attempt]'),
        expect.objectContaining({
          context: 'test-context',
          file: expect.objectContaining({
            type: 'url',
            source: url,
            size: 'unknown',
          }),
        })
      )

      consoleSpy.mockRestore()
    })
  })

  describe('logPDFLoadSuccess', () => {
    beforeEach(() => {
      // Mock performance.timing
      global.performance = {
        timing: {
          navigationStart: 1000,
          domContentLoadedEventEnd: 2000,
          loadEventEnd: 3000,
        }
      } as any
    })

    it('logs successful PDF load with metrics', () => {
      const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {})

      logPDFLoadSuccess(10, 1500, 'test-context')

      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringContaining('[PDF Load Success]'),
        expect.objectContaining({
          context: 'test-context',
          numPages: 10,
          loadTime: '1500ms',
          performance: expect.objectContaining({
            timing: expect.any(Object),
          }),
        })
      )

      consoleSpy.mockRestore()
    })

    it('handles missing performance.timing gracefully', () => {
      global.performance = {} as any
      const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {})

      logPDFLoadSuccess(5, 1000, 'test-context')

      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringContaining('[PDF Load Success]'),
        expect.objectContaining({
          context: 'test-context',
          numPages: 5,
          loadTime: '1000ms',
          performance: expect.objectContaining({
            timing: 'unavailable',
          }),
        })
      )

      consoleSpy.mockRestore()
    })
  })

  describe('handlePDFError', () => {
    beforeEach(() => {
      // Mock browser APIs for error logging
      global.navigator = {
        userAgent: 'test-agent',
        onLine: true,
        cookieEnabled: true,
        language: 'en-US',
        platform: 'test-platform',
      } as any
    })

    it('handles network errors', () => {
      const networkError = new Error('Failed to fetch')
      const result = handlePDFError(networkError, 'test-context')

      expect(result).toEqual({
        type: 'NETWORK_ERROR',
        message: 'Network error occurred while loading the PDF',
        canRetry: true,
        suggestedAction: 'Check your internet connection and try again',
      })
    })

    it('handles worker errors', () => {
      const workerError = new Error('Worker failed to initialize')
      const result = handlePDFError(workerError, 'test-context')

      expect(result).toEqual({
        type: 'WORKER_ERROR',
        message: 'PDF.js worker failed to load or execute',
        canRetry: true,
        suggestedAction: 'Check your internet connection and try reloading the page',
      })
    })

    it('handles CORS errors', () => {
      const corsError = new Error('CORS policy blocked')
      const result = handlePDFError(corsError, 'test-context')

      expect(result).toEqual({
        type: 'CORS_ERROR',
        message: 'Cross-origin request blocked',
        canRetry: true,
        suggestedAction: 'Try uploading the PDF file directly instead of using a URL',
      })
    })

    it('handles format errors', () => {
      const formatError = new Error('Invalid PDF format')
      const result = handlePDFError(formatError, 'test-context')

      expect(result).toEqual({
        type: 'FORMAT_ERROR',
        message: 'The PDF file appears to be invalid or corrupted',
        canRetry: false,
        suggestedAction: 'Try opening a different PDF file',
      })
    })

    it('handles memory errors', () => {
      const memoryError = new Error('Out of memory')
      const result = handlePDFError(memoryError, 'test-context')

      expect(result).toEqual({
        type: 'MEMORY_ERROR',
        message: 'Insufficient memory to load the PDF',
        canRetry: true,
        suggestedAction: 'Close other browser tabs and try again',
      })
    })

    it('handles unknown errors', () => {
      const unknownError = new Error('Something went wrong')
      const result = handlePDFError(unknownError, 'test-context')

      expect(result).toEqual({
        type: 'UNKNOWN_ERROR',
        message: 'Something went wrong',
        canRetry: true,
        suggestedAction: 'Try reloading the page or using a different PDF file',
      })
    })

    it('logs error details', () => {
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {})
      const error = new Error('Test error')

      handlePDFError(error, 'test-context')

      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringContaining('[PDF Load Error]'),
        expect.objectContaining({
          context: 'test-context',
          error: expect.objectContaining({
            name: error.name,
            message: error.message,
            stack: error.stack,
          }),
        })
      )

      consoleSpy.mockRestore()
    })
  })
})

"use client";

import { useState, useCallback, useRef, useEffect } from 'react';
import { toast } from 'sonner';
import type {
  DocumentInstance,
  DocumentManagerState,
  DocumentError,
  DocumentErrorType
} from '@/lib/types/pdf';
import { generateDocumentTitle, categorizeError, createDefaultDocumentMetadata } from '@/lib/types/pdf';

interface DocumentManagerProps {
  maxDocuments?: number;
  memoryThreshold?: number;
  onActiveDocumentChange?: (documentId: string | null) => void;
  onDocumentClose?: (documentId: string) => void;
}

interface DocumentManagerActions {
  openDocument: (file: string | File) => Promise<string>;
  closeDocument: (documentId: string) => void;
  setActiveDocument: (documentId: string) => void;
  getDocument: (documentId: string) => DocumentInstance | undefined;
  getActiveDocument: () => DocumentInstance | undefined;
  getAllDocuments: () => DocumentInstance[];
  updateDocument: (documentId: string, updates: Partial<DocumentInstance>) => void;
  retryDocument: (documentId: string) => Promise<void>;
  clearAllDocuments: () => void;
}

export function useDocumentManager({
  maxDocuments = 5,
  memoryThreshold = 100 * 1024 * 1024, // 100MB
  onActiveDocumentChange,
  onDocumentClose
}: DocumentManagerProps = {}): [DocumentManagerState, DocumentManagerActions] {
  
  const [state, setState] = useState<DocumentManagerState>({
    documents: new Map(),
    activeDocumentId: null,
    maxDocuments,
    memoryThreshold
  });

  const documentCounterRef = useRef(0);

  // Generate unique document ID
  const generateDocumentId = useCallback(() => {
    return `doc_${Date.now()}_${++documentCounterRef.current}`;
  }, []);

  // Memory management - cleanup least recently used documents
  const cleanupDocuments = useCallback(() => {
    const documents = Array.from(state.documents.values());
    if (documents.length <= maxDocuments) return;

    // Sort by last accessed time (oldest first)
    const sortedDocs = documents.sort((a, b) => a.lastAccessed - b.lastAccessed);
    const docsToRemove = sortedDocs.slice(0, documents.length - maxDocuments);

    setState(prev => {
      const newDocuments = new Map(prev.documents);
      docsToRemove.forEach(doc => {
        newDocuments.delete(doc.id);
        toast.info(`Closed document: ${doc.title}`, {
          description: 'Document was closed to free up memory'
        });
      });
      return { ...prev, documents: newDocuments };
    });
  }, [state.documents, maxDocuments]);

  // Open a new document
  const openDocument = useCallback(async (file: string | File): Promise<string> => {
    const documentId = generateDocumentId();
    const title = generateDocumentTitle(file);

    // Check if document is already open (by file reference)
    const existingDoc = Array.from(state.documents.values()).find(doc => {
      if (typeof file === 'string' && typeof doc.file === 'string') {
        return doc.file === file;
      }
      if (file instanceof File && doc.file instanceof File) {
        return doc.file.name === file.name && doc.file.size === file.size;
      }
      return false;
    });

    if (existingDoc) {
      // Switch to existing document
      setState(prev => ({ ...prev, activeDocumentId: existingDoc.id }));
      onActiveDocumentChange?.(existingDoc.id);
      toast.info(`Switched to existing document: ${existingDoc.title}`);
      return existingDoc.id;
    }

    // Create new document instance
    const newDocument: DocumentInstance = {
      id: documentId,
      file,
      title,
      isLoading: true,
      hasError: false,
      numPages: 0,
      lastAccessed: Date.now(),
      metadata: createDefaultDocumentMetadata(file),
      pageNumber: 1,
      scale: 1.0,
      rotation: 0,
      searchText: '',
      bookmarks: [],
      annotations: [],
      formData: {}
    };

    setState(prev => {
      const newDocuments = new Map(prev.documents);
      newDocuments.set(documentId, newDocument);
      return {
        ...prev,
        documents: newDocuments,
        activeDocumentId: documentId
      };
    });

    onActiveDocumentChange?.(documentId);
    
    // Cleanup if needed
    setTimeout(cleanupDocuments, 100);

    toast.success(`Opening document: ${title}`);
    return documentId;
  }, [generateDocumentId, state.documents, onActiveDocumentChange, cleanupDocuments]);

  // Close a document
  const closeDocument = useCallback((documentId: string) => {
    const document = state.documents.get(documentId);
    if (!document) return;

    setState(prev => {
      const newDocuments = new Map(prev.documents);
      newDocuments.delete(documentId);
      
      let newActiveId = prev.activeDocumentId;
      if (prev.activeDocumentId === documentId) {
        // Find another document to activate
        const remainingDocs = Array.from(newDocuments.keys());
        newActiveId = remainingDocs.length > 0 ? remainingDocs[0] : null;
      }

      return {
        ...prev,
        documents: newDocuments,
        activeDocumentId: newActiveId
      };
    });

    onDocumentClose?.(documentId);
    if (state.activeDocumentId === documentId) {
      const remainingDocs = Array.from(state.documents.keys()).filter(id => id !== documentId);
      onActiveDocumentChange?.(remainingDocs.length > 0 ? remainingDocs[0] : null);
    }

    toast.success(`Closed document: ${document.title}`);
  }, [state.documents, state.activeDocumentId, onDocumentClose, onActiveDocumentChange]);

  // Set active document
  const setActiveDocument = useCallback((documentId: string) => {
    if (!state.documents.has(documentId)) return;

    setState(prev => {
      const newDocuments = new Map(prev.documents);
      const document = newDocuments.get(documentId);
      if (document) {
        document.lastAccessed = Date.now();
        document.metadata.lastAccessedDate = new Date();
        newDocuments.set(documentId, document);
      }

      return {
        ...prev,
        documents: newDocuments,
        activeDocumentId: documentId
      };
    });

    onActiveDocumentChange?.(documentId);
  }, [state.documents, onActiveDocumentChange]);

  // Get specific document
  const getDocument = useCallback((documentId: string) => {
    return state.documents.get(documentId);
  }, [state.documents]);

  // Get active document
  const getActiveDocument = useCallback(() => {
    return state.activeDocumentId ? state.documents.get(state.activeDocumentId) : undefined;
  }, [state.documents, state.activeDocumentId]);

  // Get all documents
  const getAllDocuments = useCallback(() => {
    return Array.from(state.documents.values());
  }, [state.documents]);

  // Update document
  const updateDocument = useCallback((documentId: string, updates: Partial<DocumentInstance>) => {
    setState(prev => {
      const newDocuments = new Map(prev.documents);
      const document = newDocuments.get(documentId);
      if (document) {
        const updatedDocument = { ...document, ...updates, lastAccessed: Date.now() };
        newDocuments.set(documentId, updatedDocument);
      }
      return { ...prev, documents: newDocuments };
    });
  }, []);

  // Retry loading a failed document
  const retryDocument = useCallback(async (documentId: string) => {
    const document = state.documents.get(documentId);
    if (!document) return;

    updateDocument(documentId, {
      isLoading: true,
      hasError: false,
      errorMessage: undefined
    });

    toast.info(`Retrying document: ${document.title}`);
  }, [state.documents, updateDocument]);

  // Clear all documents
  const clearAllDocuments = useCallback(() => {
    setState(prev => ({
      ...prev,
      documents: new Map(),
      activeDocumentId: null
    }));
    onActiveDocumentChange?.(null);
    toast.success('All documents closed');
  }, [onActiveDocumentChange]);

  const actions: DocumentManagerActions = {
    openDocument,
    closeDocument,
    setActiveDocument,
    getDocument,
    getActiveDocument,
    getAllDocuments,
    updateDocument,
    retryDocument,
    clearAllDocuments
  };

  return [state, actions];
}

export default useDocumentManager;

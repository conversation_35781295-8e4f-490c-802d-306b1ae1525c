"use client";

import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import {
  Search,
  FileText,
  Star,
  Pin,
  Clock,
  Plus,
  Folder,
  Eye,
  MoreHorizontal,
  Trash2,
  Download,
  Tag,
  Calendar,
  User,
  BookOpen
} from 'lucide-react';
import { toast } from 'sonner';
import { cn } from '@/lib/utils';
import type {
  DocumentInstance,
  DocumentCollection
} from '@/lib/types/pdf';
import { formatFileSize } from '@/lib/types/pdf';
import { documentLibrary } from '@/lib/document-library';

interface DocumentLibrarySidebarProps {
  onDocumentSelect: (document: DocumentInstance) => void;
  onDocumentOpen: (document: DocumentInstance) => void;
  onNewDocument?: () => void;
  className?: string;
}

export default function DocumentLibrarySidebar({
  onDocumentSelect,
  onDocumentOpen,
  onNewDocument,
  className
}: DocumentLibrarySidebarProps) {
  // State
  const [documents, setDocuments] = useState<DocumentInstance[]>([]);
  const [collections, setCollections] = useState<DocumentCollection[]>([]);
  const [recentDocuments, setRecentDocuments] = useState<DocumentInstance[]>([]);
  const [favoriteDocuments, setFavoriteDocuments] = useState<DocumentInstance[]>([]);
  const [pinnedDocuments, setPinnedDocuments] = useState<DocumentInstance[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCollection, setSelectedCollection] = useState<string>('all');
  const [isLoading, setIsLoading] = useState(true);

  // Load data
  useEffect(() => {
    loadLibraryData();
  }, []);

  const loadLibraryData = async () => {
    try {
      setIsLoading(true);
      
      await documentLibrary.initialize();
      await documentLibrary.initializeDefaultCollections();

      const [docsData, collectionsData, recentData, favoriteData, pinnedData] = await Promise.all([
        documentLibrary.getAllDocuments(),
        documentLibrary.getAllCollections(),
        documentLibrary.getRecentDocuments(10),
        documentLibrary.getFavoriteDocuments(),
        documentLibrary.getPinnedDocuments()
      ]);

      setDocuments(docsData);
      setCollections(collectionsData);
      setRecentDocuments(recentData);
      setFavoriteDocuments(favoriteData);
      setPinnedDocuments(pinnedData);
    } catch (error) {
      console.error('Failed to load library data:', error);
      toast.error('Failed to load document library');
    } finally {
      setIsLoading(false);
    }
  };

  // Filter documents based on search and collection
  const filteredDocuments = React.useMemo(() => {
    let filtered = documents;

    // Collection filter
    if (selectedCollection === 'recent') {
      filtered = recentDocuments;
    } else if (selectedCollection === 'favorites') {
      filtered = favoriteDocuments;
    } else if (selectedCollection === 'pinned') {
      filtered = pinnedDocuments;
    } else if (selectedCollection !== 'all') {
      const collection = collections.find(c => c.id === selectedCollection);
      if (collection) {
        filtered = documents.filter(doc => collection.documentIds.includes(doc.id));
      }
    }

    // Search filter
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(doc =>
        doc.metadata.title.toLowerCase().includes(query) ||
        doc.metadata.author?.toLowerCase().includes(query) ||
        doc.metadata.tags.some(tag => tag.toLowerCase().includes(query))
      );
    }

    return filtered.slice(0, 20); // Limit for sidebar
  }, [documents, collections, recentDocuments, favoriteDocuments, pinnedDocuments, selectedCollection, searchQuery]);

  const handleDocumentClick = (document: DocumentInstance) => {
    onDocumentSelect(document);
  };

  const handleDocumentOpen = (document: DocumentInstance) => {
    onDocumentOpen(document);
    // Update access time
    documentLibrary.updateDocument(document.id, {
      metadata: {
        ...document.metadata,
        lastAccessedDate: new Date(),
        openCount: document.metadata.openCount + 1
      }
    });
    loadLibraryData(); // Refresh data
  };

  const handleToggleFavorite = async (document: DocumentInstance, e: React.MouseEvent) => {
    e.stopPropagation();
    try {
      await documentLibrary.updateDocument(document.id, {
        metadata: {
          ...document.metadata,
          isFavorite: !document.metadata.isFavorite
        }
      });
      await loadLibraryData();
    } catch (err) {
      toast.error('Failed to update favorite status');
    }
  };

  if (isLoading) {
    return (
      <div className={cn("p-4", className)}>
        <div className="flex items-center justify-center h-32">
          <div className="text-center">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary mx-auto mb-2"></div>
            <p className="text-xs text-muted-foreground">Loading library...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={cn("flex flex-col h-full", className)}>
      {/* Header */}
      <div className="p-4 border-b">
        <div className="flex items-center justify-between mb-3">
          <h3 className="font-semibold text-sm">Document Library</h3>
          {onNewDocument && (
            <Button size="sm" variant="outline" onClick={onNewDocument}>
              <Plus className="h-3 w-3 mr-1" />
              Add
            </Button>
          )}
        </div>

        {/* Search */}
        <div className="relative">
          <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 h-3 w-3 text-muted-foreground" />
          <Input
            placeholder="Search documents..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-7 h-8 text-xs"
          />
        </div>
      </div>

      {/* Quick Collections */}
      <div className="p-4 border-b">
        <div className="space-y-1">
          <Button
            variant={selectedCollection === 'all' ? 'secondary' : 'ghost'}
            size="sm"
            className="w-full justify-start h-8 text-xs"
            onClick={() => setSelectedCollection('all')}
          >
            <FileText className="h-3 w-3 mr-2" />
            All Documents
            <Badge variant="outline" className="ml-auto text-xs">
              {documents.length}
            </Badge>
          </Button>
          
          <Button
            variant={selectedCollection === 'recent' ? 'secondary' : 'ghost'}
            size="sm"
            className="w-full justify-start h-8 text-xs"
            onClick={() => setSelectedCollection('recent')}
          >
            <Clock className="h-3 w-3 mr-2" />
            Recent
            <Badge variant="outline" className="ml-auto text-xs">
              {recentDocuments.length}
            </Badge>
          </Button>
          
          <Button
            variant={selectedCollection === 'favorites' ? 'secondary' : 'ghost'}
            size="sm"
            className="w-full justify-start h-8 text-xs"
            onClick={() => setSelectedCollection('favorites')}
          >
            <Star className="h-3 w-3 mr-2" />
            Favorites
            <Badge variant="outline" className="ml-auto text-xs">
              {favoriteDocuments.length}
            </Badge>
          </Button>
          
          <Button
            variant={selectedCollection === 'pinned' ? 'secondary' : 'ghost'}
            size="sm"
            className="w-full justify-start h-8 text-xs"
            onClick={() => setSelectedCollection('pinned')}
          >
            <Pin className="h-3 w-3 mr-2" />
            Pinned
            <Badge variant="outline" className="ml-auto text-xs">
              {pinnedDocuments.length}
            </Badge>
          </Button>
        </div>

        {/* Custom Collections */}
        {collections.filter(c => !c.isSystem).length > 0 && (
          <>
            <Separator className="my-2" />
            <div className="space-y-1">
              {collections.filter(c => !c.isSystem).map(collection => (
                <Button
                  key={collection.id}
                  variant={selectedCollection === collection.id ? 'secondary' : 'ghost'}
                  size="sm"
                  className="w-full justify-start h-8 text-xs"
                  onClick={() => setSelectedCollection(collection.id)}
                >
                  <Folder className="h-3 w-3 mr-2" />
                  {collection.name}
                  <Badge variant="outline" className="ml-auto text-xs">
                    {collection.documentIds.length}
                  </Badge>
                </Button>
              ))}
            </div>
          </>
        )}
      </div>

      {/* Document List */}
      <ScrollArea className="flex-1">
        <div className="p-2">
          {filteredDocuments.length === 0 ? (
            <div className="text-center py-8">
              <FileText className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
              <p className="text-xs text-muted-foreground">
                {searchQuery ? 'No documents found' : 'No documents in this collection'}
              </p>
            </div>
          ) : (
            <div className="space-y-1">
              {filteredDocuments.map(document => (
                <DocumentItem
                  key={document.id}
                  document={document}
                  onSelect={() => handleDocumentClick(document)}
                  onOpen={() => handleDocumentOpen(document)}
                  onToggleFavorite={(e) => handleToggleFavorite(document, e)}
                />
              ))}
            </div>
          )}
        </div>
      </ScrollArea>
    </div>
  );
}

// Document Item Component
interface DocumentItemProps {
  document: DocumentInstance;
  onSelect: () => void;
  onOpen: () => void;
  onToggleFavorite: (e: React.MouseEvent) => void;
}

function DocumentItem({ document, onSelect, onOpen, onToggleFavorite }: DocumentItemProps) {
  const { metadata } = document;

  return (
    <div
      className="group p-2 rounded-md hover:bg-muted/50 cursor-pointer transition-colors"
      onClick={onSelect}
    >
      <div className="flex items-start gap-2">
        <FileText className="h-4 w-4 text-muted-foreground mt-0.5 flex-shrink-0" />
        <div className="flex-1 min-w-0">
          <div className="flex items-start justify-between">
            <div className="flex-1 min-w-0">
              <p className="text-xs font-medium truncate" title={metadata.title}>
                {metadata.title}
              </p>
              {metadata.author && (
                <p className="text-xs text-muted-foreground truncate">
                  by {metadata.author}
                </p>
              )}
              <div className="flex items-center gap-2 mt-1 text-xs text-muted-foreground">
                <span>{formatFileSize(metadata.fileSize)}</span>
                <span>•</span>
                <span>{metadata.pageCount}p</span>
              </div>
            </div>
            <div className="flex items-center gap-1 ml-1">
              {metadata.isFavorite && (
                <Star className="h-3 w-3 text-yellow-500 fill-current" />
              )}
              {metadata.isPinned && (
                <Pin className="h-3 w-3 text-blue-500" />
              )}
            </div>
          </div>
          
          {/* Tags */}
          {metadata.tags.length > 0 && (
            <div className="flex flex-wrap gap-1 mt-1">
              {metadata.tags.slice(0, 2).map(tag => (
                <Badge key={tag} variant="secondary" className="text-xs px-1 py-0">
                  {tag}
                </Badge>
              ))}
              {metadata.tags.length > 2 && (
                <Badge variant="outline" className="text-xs px-1 py-0">
                  +{metadata.tags.length - 2}
                </Badge>
              )}
            </div>
          )}

          {/* Actions */}
          <div className="flex items-center gap-1 mt-1 opacity-0 group-hover:opacity-100 transition-opacity">
            <Button size="sm" variant="ghost" className="h-6 w-6 p-0" onClick={onOpen}>
              <Eye className="h-3 w-3" />
            </Button>
            <Button size="sm" variant="ghost" className="h-6 w-6 p-0" onClick={onToggleFavorite}>
              <Star className={cn(
                "h-3 w-3",
                metadata.isFavorite ? "text-yellow-500 fill-current" : "text-muted-foreground"
              )} />
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}

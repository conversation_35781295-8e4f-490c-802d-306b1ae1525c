"use client";

import type React from "react";

import { useState, useRef, useCallback } from "react";
import type { Annotation, AnnotationType } from "./pdf-annotations";
import { cn } from "@/lib/utils";

interface PDFAnnotationOverlayProps {
  pageNumber: number;
  scale: number;
  rotation: number;
  annotations: Annotation[];
  selectedTool: AnnotationType | null;
  selectedColor: string;
  onAnnotationAdd: (annotation: Omit<Annotation, "id" | "timestamp">) => void;
  onAnnotationSelect?: (annotation: Annotation) => void;
}

export default function PDFAnnotationOverlay({
  pageNumber,
  scale,
  rotation,
  annotations,
  selectedTool,
  selectedColor,
  onAnnotationAdd,
  onAnnotationSelect,
}: PDFAnnotationOverlayProps) {
  const [isDrawing, setIsDrawing] = useState(false);
  const [currentPath, setCurrentPath] = useState<{ x: number; y: number }[]>(
    []
  );
  const [startPoint, setStartPoint] = useState<{ x: number; y: number } | null>(
    null
  );
  const [previewShape, setPreviewShape] = useState<{
    x: number;
    y: number;
    width: number;
    height: number;
  } | null>(null);
  const overlayRef = useRef<HTMLDivElement>(null);

  const pageAnnotations = annotations.filter(
    (ann) => ann.pageNumber === pageNumber
  );

  const getRelativePosition = useCallback(
    (e: React.MouseEvent) => {
      if (!overlayRef.current) return { x: 0, y: 0 };

      const rect = overlayRef.current.getBoundingClientRect();
      return {
        x: (e.clientX - rect.left) / scale,
        y: (e.clientY - rect.top) / scale,
      };
    },
    [scale]
  );

  const handleMouseDown = useCallback(
    (e: React.MouseEvent) => {
      if (!selectedTool) return;

      const pos = getRelativePosition(e);
      setStartPoint(pos);

      if (selectedTool === "freehand") {
        setIsDrawing(true);
        setCurrentPath([pos]);
      } else if (selectedTool === "note" || selectedTool === "text") {
        // For notes and text, add immediately
        const content =
          selectedTool === "note" ? "New note" : "Text annotation";
        onAnnotationAdd({
          type: selectedTool,
          pageNumber,
          x: pos.x,
          y: pos.y,
          width: selectedTool === "text" ? 200 : 20,
          height: selectedTool === "text" ? 30 : 20,
          color: selectedColor,
          content,
          author: "User",
          createdAt: new Date(),
          updatedAt: new Date(),
        });
      }
    },
    [
      selectedTool,
      pageNumber,
      selectedColor,
      onAnnotationAdd,
      getRelativePosition,
    ]
  );

  const handleMouseMove = useCallback(
    (e: React.MouseEvent) => {
      if (!selectedTool || !startPoint) return;

      const pos = getRelativePosition(e);

      if (selectedTool === "freehand" && isDrawing) {
        setCurrentPath((prev) => [...prev, pos]);
      } else if (
        ["rectangle", "circle", "line", "arrow"].includes(selectedTool)
      ) {
        setPreviewShape({
          x: Math.min(startPoint.x, pos.x),
          y: Math.min(startPoint.y, pos.y),
          width: Math.abs(pos.x - startPoint.x),
          height: Math.abs(pos.y - startPoint.y),
        });
      }
    },
    [selectedTool, startPoint, isDrawing, getRelativePosition]
  );

  const handleMouseUp = useCallback(
    (e: React.MouseEvent) => {
      if (!selectedTool || !startPoint) return;

      const pos = getRelativePosition(e);

      if (selectedTool === "freehand" && currentPath.length > 1) {
        onAnnotationAdd({
          type: "freehand",
          pageNumber,
          x: startPoint.x,
          y: startPoint.y,
          color: selectedColor,
          points: currentPath,
          author: "User",
          createdAt: new Date(),
          updatedAt: new Date(),
        });
      } else if (
        ["rectangle", "circle", "line", "arrow"].includes(selectedTool)
      ) {
        const width = Math.abs(pos.x - startPoint.x);
        const height = Math.abs(pos.y - startPoint.y);

        if (width > 5 || height > 5) {
          // Minimum size threshold
          onAnnotationAdd({
            type: selectedTool as AnnotationType,
            pageNumber,
            x: Math.min(startPoint.x, pos.x),
            y: Math.min(startPoint.y, pos.y),
            width,
            height,
            color: selectedColor,
            author: "User",
            createdAt: new Date(),
            updatedAt: new Date(),
          });
        }
      }

      // Reset state
      setIsDrawing(false);
      setCurrentPath([]);
      setStartPoint(null);
      setPreviewShape(null);
    },
    [
      selectedTool,
      startPoint,
      currentPath,
      pageNumber,
      selectedColor,
      onAnnotationAdd,
      getRelativePosition,
    ]
  );

  const renderAnnotation = (annotation: Annotation) => {
    const style = {
      position: "absolute" as const,
      left: `${annotation.x * scale}px`,
      top: `${annotation.y * scale}px`,
      transform: `rotate(${rotation}deg)`,
      transformOrigin: "top left",
      pointerEvents: "auto" as const,
      cursor: "pointer",
      zIndex: 5,
    };

    switch (annotation.type) {
      case "highlight":
        return (
          <div
            key={annotation.id}
            style={{
              ...style,
              width: `${(annotation.width || 100) * scale}px`,
              height: `${(annotation.height || 20) * scale}px`,
              backgroundColor: annotation.color,
              opacity: 0.3,
              borderRadius: "2px",
            }}
            onClick={() => onAnnotationSelect?.(annotation)}
          />
        );

      case "rectangle":
        return (
          <div
            key={annotation.id}
            style={{
              ...style,
              width: `${(annotation.width || 50) * scale}px`,
              height: `${(annotation.height || 50) * scale}px`,
              border: `2px solid ${annotation.color}`,
              backgroundColor: "transparent",
            }}
            onClick={() => onAnnotationSelect?.(annotation)}
          />
        );

      case "circle":
        return (
          <div
            key={annotation.id}
            style={{
              ...style,
              width: `${(annotation.width || 50) * scale}px`,
              height: `${(annotation.height || 50) * scale}px`,
              border: `2px solid ${annotation.color}`,
              backgroundColor: "transparent",
              borderRadius: "50%",
            }}
            onClick={() => onAnnotationSelect?.(annotation)}
          />
        );

      case "arrow":
        return (
          <div
            key={annotation.id}
            style={{
              ...style,
              width: `${(annotation.width || 50) * scale}px`,
              height: "2px",
              backgroundColor: annotation.color,
              transformOrigin: "left center",
              position: "relative",
            }}
            onClick={() => onAnnotationSelect?.(annotation)}
          >
            <div
              style={{
                position: "absolute",
                right: "-6px",
                top: "-4px",
                width: "0",
                height: "0",
                borderLeft: `6px solid ${annotation.color}`,
                borderTop: "4px solid transparent",
                borderBottom: "4px solid transparent",
              }}
            />
          </div>
        );

      case "line":
        return (
          <div
            key={annotation.id}
            style={{
              ...style,
              width: `${(annotation.width || 50) * scale}px`,
              height: "2px",
              backgroundColor: annotation.color,
              transformOrigin: "left center",
            }}
            onClick={() => onAnnotationSelect?.(annotation)}
          />
        );

      case "note":
        return (
          <div
            key={annotation.id}
            style={{
              ...style,
              width: "20px",
              height: "20px",
              backgroundColor: annotation.color,
              borderRadius: "50%",
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              fontSize: "12px",
              fontWeight: "bold",
              color: "white",
              boxShadow: "0 2px 4px rgba(0,0,0,0.2)",
            }}
            onClick={() => onAnnotationSelect?.(annotation)}
            title={annotation.content}
          >
            💬
          </div>
        );

      case "text":
        return (
          <div
            key={annotation.id}
            style={{
              ...style,
              minWidth: `${(annotation.width || 200) * scale}px`,
              minHeight: `${(annotation.height || 30) * scale}px`,
              backgroundColor: "rgba(255, 255, 255, 0.9)",
              border: `1px solid ${annotation.color}`,
              borderRadius: "4px",
              padding: `${4 * scale}px`,
              fontSize: `${12 * scale}px`,
              color: annotation.color,
              boxShadow: "0 2px 4px rgba(0,0,0,0.1)",
            }}
            onClick={() => onAnnotationSelect?.(annotation)}
          >
            {annotation.content}
          </div>
        );

      case "freehand":
        if (!annotation.points || annotation.points.length < 2) return null;

        const pathData = annotation.points
          .map(
            (point, index) =>
              `${index === 0 ? "M" : "L"} ${point.x * scale} ${point.y * scale}`
          )
          .join(" ");

        return (
          <svg
            key={annotation.id}
            style={{
              position: "absolute",
              left: 0,
              top: 0,
              width: "100%",
              height: "100%",
              pointerEvents: "none",
            }}
          >
            <path
              d={pathData}
              stroke={annotation.color}
              strokeWidth={2 * scale}
              fill="none"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </svg>
        );

      default:
        return null;
    }
  };

  const renderPreview = () => {
    if (!selectedTool || !startPoint) return null;

    if (selectedTool === "freehand" && currentPath.length > 1) {
      const pathData = currentPath
        .map(
          (point, index) =>
            `${index === 0 ? "M" : "L"} ${point.x * scale} ${point.y * scale}`
        )
        .join(" ");

      return (
        <svg
          style={{
            position: "absolute",
            left: 0,
            top: 0,
            width: "100%",
            height: "100%",
            pointerEvents: "none",
          }}
        >
          <path
            d={pathData}
            stroke={selectedColor}
            strokeWidth={2 * scale}
            fill="none"
            strokeLinecap="round"
            strokeLinejoin="round"
            opacity={0.7}
          />
        </svg>
      );
    }

    if (
      previewShape &&
      ["rectangle", "circle", "line", "arrow"].includes(selectedTool)
    ) {
      const style = {
        position: "absolute" as const,
        left: `${previewShape.x * scale}px`,
        top: `${previewShape.y * scale}px`,
        width: `${previewShape.width * scale}px`,
        height:
          selectedTool === "line" || selectedTool === "arrow"
            ? "2px"
            : `${previewShape.height * scale}px`,
        border: `2px dashed ${selectedColor}`,
        backgroundColor: "transparent",
        borderRadius: selectedTool === "circle" ? "50%" : "0",
        pointerEvents: "none" as const,
        opacity: 0.7,
      };

      return <div style={style} />;
    }

    return null;
  };

  return (
    <div
      ref={overlayRef}
      className={cn(
        "absolute inset-0",
        selectedTool
          ? "pointer-events-auto cursor-crosshair"
          : "pointer-events-none"
      )}
      style={{
        zIndex: selectedTool ? 10 : 1,
        cursor: selectedTool ? "crosshair" : "default",
      }}
      onMouseDown={selectedTool ? handleMouseDown : undefined}
      onMouseMove={selectedTool ? handleMouseMove : undefined}
      onMouseUp={selectedTool ? handleMouseUp : undefined}
    >
      {/* Existing annotations */}
      {pageAnnotations.map(renderAnnotation)}

      {/* Preview for current drawing */}
      {renderPreview()}
    </div>
  );
}

/**
 * Mobile Testing Utilities
 * Provides utilities for testing mobile responsiveness and touch interactions
 */

export interface MobileBreakpoint {
  name: string;
  width: number;
  height: number;
  devicePixelRatio: number;
  userAgent: string;
  description: string;
}

export const MOBILE_BREAKPOINTS: Record<string, MobileBreakpoint> = {
  iphone5: {
    name: 'iPhone 5/SE',
    width: 320,
    height: 568,
    devicePixelRatio: 2,
    userAgent: 'Mozilla/5.0 (iPhone; CPU iPhone OS 10_3_1 like Mac OS X) AppleWebKit/603.1.30 (KHTML, like Gecko) Version/10.0 Mobile/14E304 Safari/602.1',
    description: 'Smallest modern iPhone screen'
  },
  iphone6: {
    name: 'iPhone 6/7/8',
    width: 375,
    height: 667,
    devicePixelRatio: 2,
    userAgent: 'Mozilla/5.0 (iPhone; CPU iPhone OS 13_2_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.0.3 Mobile/15E148 Safari/604.1',
    description: 'Standard iPhone screen'
  },
  iphoneX: {
    name: 'iPhone X/11/12/13',
    width: 414,
    height: 896,
    devicePixelRatio: 3,
    userAgent: 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_7_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.2 Mobile/15E148 Safari/604.1',
    description: 'Modern iPhone with notch'
  },
  androidSmall: {
    name: 'Android Small',
    width: 360,
    height: 640,
    devicePixelRatio: 2,
    userAgent: 'Mozilla/5.0 (Linux; Android 9; SM-G960F) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36',
    description: 'Small Android device'
  },
  androidMedium: {
    name: 'Android Medium',
    width: 412,
    height: 732,
    devicePixelRatio: 2.625,
    userAgent: 'Mozilla/5.0 (Linux; Android 10; Pixel 3) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36',
    description: 'Medium Android device'
  },
  tablet: {
    name: 'iPad',
    width: 768,
    height: 1024,
    devicePixelRatio: 2,
    userAgent: 'Mozilla/5.0 (iPad; CPU OS 13_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) CriOS/87.0.4280.77 Mobile/15E148 Safari/604.1',
    description: 'Tablet breakpoint'
  }
};

export interface TouchTargetTest {
  element: HTMLElement;
  expectedMinSize: number;
  actualSize: { width: number; height: number };
  passes: boolean;
}

export interface ResponsiveTest {
  breakpoint: MobileBreakpoint;
  viewport: { width: number; height: number };
  elements: {
    visible: HTMLElement[];
    hidden: HTMLElement[];
    touchTargets: TouchTargetTest[];
  };
  layout: {
    hasHorizontalScroll: boolean;
    hasVerticalScroll: boolean;
    contentFitsViewport: boolean;
  };
  performance: {
    renderTime: number;
    layoutShifts: number;
  };
}

/**
 * Set viewport size for testing
 */
export function setViewportSize(width: number, height: number): void {
  if (typeof window !== 'undefined') {
    // For browser testing
    Object.defineProperty(window, 'innerWidth', {
      writable: true,
      configurable: true,
      value: width,
    });
    Object.defineProperty(window, 'innerHeight', {
      writable: true,
      configurable: true,
      value: height,
    });
    
    // Trigger resize event
    window.dispatchEvent(new Event('resize'));
  }
}

/**
 * Check if element meets touch target requirements
 */
export function checkTouchTarget(element: HTMLElement, minSize: number = 44): TouchTargetTest {
  const rect = element.getBoundingClientRect();
  const actualSize = {
    width: rect.width,
    height: rect.height
  };
  
  const passes = actualSize.width >= minSize && actualSize.height >= minSize;
  
  return {
    element,
    expectedMinSize: minSize,
    actualSize,
    passes
  };
}

/**
 * Check for horizontal scrolling
 */
export function hasHorizontalScroll(): boolean {
  if (typeof document === 'undefined') return false;
  return document.documentElement.scrollWidth > document.documentElement.clientWidth;
}

/**
 * Check if content fits in viewport
 */
export function contentFitsViewport(): boolean {
  if (typeof document === 'undefined') return true;
  
  const body = document.body;
  const html = document.documentElement;
  
  const documentWidth = Math.max(
    body.scrollWidth,
    body.offsetWidth,
    html.clientWidth,
    html.scrollWidth,
    html.offsetWidth
  );
  
  return documentWidth <= window.innerWidth;
}

/**
 * Simulate touch events
 */
export function simulateTouch(element: HTMLElement, type: 'tap' | 'swipe' | 'pinch', options: any = {}): void {
  const { x = 0, y = 0, direction = 'left', scale = 1.5 } = options;
  
  switch (type) {
    case 'tap':
      const touchStart = new TouchEvent('touchstart', {
        touches: [new Touch({
          identifier: 0,
          target: element,
          clientX: x,
          clientY: y,
          radiusX: 10,
          radiusY: 10,
          rotationAngle: 0,
          force: 1
        })]
      });
      
      const touchEnd = new TouchEvent('touchend', {
        changedTouches: [new Touch({
          identifier: 0,
          target: element,
          clientX: x,
          clientY: y,
          radiusX: 10,
          radiusY: 10,
          rotationAngle: 0,
          force: 1
        })]
      });
      
      element.dispatchEvent(touchStart);
      setTimeout(() => element.dispatchEvent(touchEnd), 100);
      break;
      
    case 'swipe':
      // Implement swipe simulation
      const startX = direction === 'left' ? x + 100 : x;
      const endX = direction === 'left' ? x : x + 100;
      
      // Simulate swipe gesture
      element.dispatchEvent(new TouchEvent('touchstart', {
        touches: [new Touch({
          identifier: 0,
          target: element,
          clientX: startX,
          clientY: y,
          radiusX: 10,
          radiusY: 10,
          rotationAngle: 0,
          force: 1
        })]
      }));
      
      setTimeout(() => {
        element.dispatchEvent(new TouchEvent('touchend', {
          changedTouches: [new Touch({
            identifier: 0,
            target: element,
            clientX: endX,
            clientY: y,
            radiusX: 10,
            radiusY: 10,
            rotationAngle: 0,
            force: 1
          })]
        }));
      }, 200);
      break;
      
    case 'pinch':
      // Implement pinch simulation for zoom testing
      // This would require more complex multi-touch simulation
      break;
  }
}

/**
 * Test component across multiple breakpoints
 */
export async function testResponsiveComponent(
  component: HTMLElement,
  breakpoints: MobileBreakpoint[] = Object.values(MOBILE_BREAKPOINTS)
): Promise<ResponsiveTest[]> {
  const results: ResponsiveTest[] = [];
  
  for (const breakpoint of breakpoints) {
    setViewportSize(breakpoint.width, breakpoint.height);
    
    // Wait for layout to settle
    await new Promise(resolve => setTimeout(resolve, 100));
    
    const touchTargets = Array.from(component.querySelectorAll('button, [role="button"], input, textarea, select'))
      .map(el => checkTouchTarget(el as HTMLElement));
    
    const result: ResponsiveTest = {
      breakpoint,
      viewport: { width: breakpoint.width, height: breakpoint.height },
      elements: {
        visible: Array.from(component.querySelectorAll(':not([hidden]):not(.hidden)')),
        hidden: Array.from(component.querySelectorAll('[hidden], .hidden')),
        touchTargets
      },
      layout: {
        hasHorizontalScroll: hasHorizontalScroll(),
        hasVerticalScroll: document.documentElement.scrollHeight > window.innerHeight,
        contentFitsViewport: contentFitsViewport()
      },
      performance: {
        renderTime: 0, // Would need performance API integration
        layoutShifts: 0 // Would need layout shift detection
      }
    };
    
    results.push(result);
  }
  
  return results;
}

/**
 * Generate mobile testing report
 */
export function generateMobileTestReport(results: ResponsiveTest[]): string {
  let report = '# Mobile Responsiveness Test Report\n\n';
  
  results.forEach(result => {
    report += `## ${result.breakpoint.name} (${result.breakpoint.width}x${result.breakpoint.height})\n\n`;
    
    // Layout issues
    if (result.layout.hasHorizontalScroll) {
      report += '❌ **Issue**: Horizontal scrolling detected\n';
    } else {
      report += '✅ No horizontal scrolling\n';
    }
    
    // Touch targets
    const failedTouchTargets = result.elements.touchTargets.filter(t => !t.passes);
    if (failedTouchTargets.length > 0) {
      report += `❌ **Issue**: ${failedTouchTargets.length} touch targets below 44px minimum\n`;
    } else {
      report += '✅ All touch targets meet 44px minimum\n';
    }
    
    report += '\n';
  });
  
  return report;
}

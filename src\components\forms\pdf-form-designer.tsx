"use client";

import { useState, useCallback } from "react";
import {
  Card<PERSON>ontent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  Plus,
  Edit,
  Trash2,
  Copy,
  Settings,
  Type,
  CheckSquare,
  List,
  Calendar,
  Hash,
  Mail,
  PenTool,
} from "lucide-react";
import { toast } from "sonner";
import type { FormField, FormFieldType } from "./pdf-form-manager";

interface PDFFormDesignerProps {
  formFields: FormField[];
  currentPage: number;
  onFormFieldsChange: (fields: FormField[]) => void;
}

export default function PDFFormDesigner({
  formFields,
  currentPage,
  onFormFieldsChange,
}: PDFFormDesignerProps) {
  const [selectedField, setSelectedField] = useState<FormField | null>(null);
  const [newFieldType, setNewFieldType] = useState<FormFieldType>("text");
  const [isCreating, setIsCreating] = useState<boolean>(false);

  const fieldTypes = [
    { value: "text", label: "Text Input", icon: Type },
    { value: "textarea", label: "Text Area", icon: Type },
    { value: "checkbox", label: "Checkbox", icon: CheckSquare },
    { value: "select", label: "Dropdown", icon: List },
    { value: "date", label: "Date", icon: Calendar },
    { value: "number", label: "Number", icon: Hash },
    { value: "email", label: "Email", icon: Mail },
    { value: "signature", label: "Signature", icon: PenTool },
  ];

  const createNewField = useCallback(() => {
    setIsCreating(true);

    const newField: FormField = {
      id: `field-${Date.now()}`,
      name: `field_${formFields.length + 1}`,
      type: newFieldType,
      value: "",
      required: false,
      readonly: false,
      position: {
        pageNumber: currentPage,
        x: 100,
        y: 100,
        width: 200,
        height: 30,
      },
      appearance: {
        fontSize: 12,
        fontColor: "#000000",
        backgroundColor: "#ffffff",
        borderColor: "#cccccc",
        borderWidth: 1,
      },
      metadata: {
        created: Date.now(),
        modified: Date.now(),
      },
    };

    const updatedFields = [...formFields, newField];
    onFormFieldsChange(updatedFields);
    setSelectedField(newField);
    setIsCreating(false);

    toast(`New ${newFieldType} field added to page ${currentPage}`);
  }, [formFields, newFieldType, currentPage, onFormFieldsChange]);

  const updateField = useCallback(
    (fieldId: string, updates: Partial<FormField>) => {
      const updatedFields = formFields.map((field) =>
        field.id === fieldId
          ? {
              ...field,
              ...updates,
              metadata: {
                ...field.metadata,
                modified: Date.now(),
              },
            }
          : field
      );
      onFormFieldsChange(updatedFields);

      if (selectedField?.id === fieldId) {
        setSelectedField({ ...selectedField, ...updates });
      }
    },
    [formFields, selectedField, onFormFieldsChange]
  );

  const deleteField = useCallback(
    (fieldId: string) => {
      const updatedFields = formFields.filter((field) => field.id !== fieldId);
      onFormFieldsChange(updatedFields);

      if (selectedField?.id === fieldId) {
        setSelectedField(null);
      }

      toast("Form field has been removed");
    },
    [formFields, selectedField, onFormFieldsChange]
  );

  const duplicateField = useCallback(
    (field: FormField) => {
      const duplicatedField: FormField = {
        ...field,
        id: `field-${Date.now()}`,
        name: `${field.name}_copy`,
        position: {
          ...field.position,
          y: field.position.y + 40,
        },
        metadata: {
          created: Date.now(),
          modified: Date.now(),
        },
      };

      const updatedFields = [...formFields, duplicatedField];
      onFormFieldsChange(updatedFields);

      toast("Field has been copied successfully");
    },
    [formFields, onFormFieldsChange]
  );

  const currentPageFields = formFields.filter(
    (field) => field.position.pageNumber === currentPage
  );

  return (
    <div className="h-full flex flex-col">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Settings className="h-5 w-5" />
          Form Designer
        </CardTitle>
        <CardDescription>
          Create and edit interactive form fields
        </CardDescription>

        {/* Create New Field */}
        <div className="space-y-3">
          <div className="flex items-center gap-2">
            <Select
              value={newFieldType}
              onValueChange={(value: FormFieldType) => setNewFieldType(value)}
            >
              <SelectTrigger className="flex-1">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {fieldTypes.map((type) => {
                  const Icon = type.icon;
                  return (
                    <SelectItem key={type.value} value={type.value}>
                      <div className="flex items-center gap-2">
                        <Icon className="h-4 w-4" />
                        {type.label}
                      </div>
                    </SelectItem>
                  );
                })}
              </SelectContent>
            </Select>
            <Button onClick={createNewField} disabled={isCreating}>
              <Plus className="h-4 w-4 mr-2" />
              {isCreating ? "Adding..." : "Add Field"}
            </Button>
          </div>
        </div>
      </CardHeader>

      <CardContent className="flex-1 overflow-hidden">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 h-full">
          {/* Field List */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h3 className="font-medium">Fields on Page {currentPage}</h3>
              <Badge variant="outline">{currentPageFields.length}</Badge>
            </div>

            <ScrollArea className="h-[400px]">
              <div className="space-y-2">
                {currentPageFields.map((field) => {
                  const FieldIcon =
                    fieldTypes.find((t) => t.value === field.type)?.icon ||
                    Type;
                  return (
                    <div
                      key={field.id}
                      className={`p-3 border rounded-lg cursor-pointer transition-colors ${
                        selectedField?.id === field.id
                          ? "border-primary bg-primary/5"
                          : "hover:bg-muted/50"
                      }`}
                      onClick={() => setSelectedField(field)}
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <FieldIcon className="h-4 w-4" />
                          <div>
                            <div className="font-medium text-sm">
                              {field.name}
                            </div>
                            <div className="text-xs text-muted-foreground">
                              {field.type} •{" "}
                              {field.required ? "Required" : "Optional"}
                            </div>
                          </div>
                        </div>
                        <div className="flex items-center gap-1">
                          <Button
                            size="sm"
                            variant="ghost"
                            className="h-6 w-6 p-0"
                            onClick={(e) => {
                              e.stopPropagation();
                              duplicateField(field);
                            }}
                          >
                            <Copy className="h-3 w-3" />
                          </Button>
                          <Button
                            size="sm"
                            variant="ghost"
                            className="h-6 w-6 p-0 text-destructive hover:text-destructive"
                            onClick={(e) => {
                              e.stopPropagation();
                              deleteField(field.id);
                            }}
                          >
                            <Trash2 className="h-3 w-3" />
                          </Button>
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            </ScrollArea>
          </div>

          {/* Field Properties */}
          <div className="space-y-4">
            {selectedField ? (
              <>
                <div className="flex items-center justify-between">
                  <h3 className="font-medium">Field Properties</h3>
                  <Badge variant="secondary">{selectedField.type}</Badge>
                </div>

                <ScrollArea className="h-[400px]">
                  <div className="space-y-4">
                    {/* Basic Properties */}
                    <div className="space-y-3">
                      <h4 className="text-sm font-medium">Basic</h4>

                      <div className="space-y-2">
                        <Label htmlFor="field-name">Field Name</Label>
                        <Input
                          id="field-name"
                          value={selectedField.name}
                          onChange={(e) =>
                            updateField(selectedField.id, {
                              name: e.target.value,
                            })
                          }
                        />
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="field-placeholder">Placeholder</Label>
                        <Input
                          id="field-placeholder"
                          value={selectedField.metadata.placeholder || ""}
                          onChange={(e) =>
                            updateField(selectedField.id, {
                              metadata: {
                                ...selectedField.metadata,
                                placeholder: e.target.value,
                              },
                            })
                          }
                        />
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="field-tooltip">Tooltip</Label>
                        <Input
                          id="field-tooltip"
                          value={selectedField.metadata.tooltip || ""}
                          onChange={(e) =>
                            updateField(selectedField.id, {
                              metadata: {
                                ...selectedField.metadata,
                                tooltip: e.target.value,
                              },
                            })
                          }
                        />
                      </div>

                      <div className="flex items-center space-x-2">
                        <Checkbox
                          id="field-required"
                          checked={selectedField.required}
                          onCheckedChange={(checked) =>
                            updateField(selectedField.id, {
                              required: checked as boolean,
                            })
                          }
                        />
                        <Label htmlFor="field-required">Required field</Label>
                      </div>

                      <div className="flex items-center space-x-2">
                        <Checkbox
                          id="field-readonly"
                          checked={selectedField.readonly}
                          onCheckedChange={(checked) =>
                            updateField(selectedField.id, {
                              readonly: checked as boolean,
                            })
                          }
                        />
                        <Label htmlFor="field-readonly">Read-only</Label>
                      </div>
                    </div>

                    <Separator />

                    {/* Position Properties */}
                    <div className="space-y-3">
                      <h4 className="text-sm font-medium">Position</h4>

                      <div className="grid grid-cols-2 gap-2">
                        <div className="space-y-2">
                          <Label htmlFor="field-x">X Position</Label>
                          <Input
                            id="field-x"
                            type="number"
                            value={selectedField.position.x}
                            onChange={(e) =>
                              updateField(selectedField.id, {
                                position: {
                                  ...selectedField.position,
                                  x: Number(e.target.value),
                                },
                              })
                            }
                          />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="field-y">Y Position</Label>
                          <Input
                            id="field-y"
                            type="number"
                            value={selectedField.position.y}
                            onChange={(e) =>
                              updateField(selectedField.id, {
                                position: {
                                  ...selectedField.position,
                                  y: Number(e.target.value),
                                },
                              })
                            }
                          />
                        </div>
                      </div>

                      <div className="grid grid-cols-2 gap-2">
                        <div className="space-y-2">
                          <Label htmlFor="field-width">Width</Label>
                          <Input
                            id="field-width"
                            type="number"
                            value={selectedField.position.width}
                            onChange={(e) =>
                              updateField(selectedField.id, {
                                position: {
                                  ...selectedField.position,
                                  width: Number(e.target.value),
                                },
                              })
                            }
                          />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="field-height">Height</Label>
                          <Input
                            id="field-height"
                            type="number"
                            value={selectedField.position.height}
                            onChange={(e) =>
                              updateField(selectedField.id, {
                                position: {
                                  ...selectedField.position,
                                  height: Number(e.target.value),
                                },
                              })
                            }
                          />
                        </div>
                      </div>
                    </div>

                    <Separator />

                    {/* Appearance Properties */}
                    <div className="space-y-3">
                      <h4 className="text-sm font-medium">Appearance</h4>

                      <div className="space-y-2">
                        <Label htmlFor="field-font-size">Font Size</Label>
                        <Input
                          id="field-font-size"
                          type="number"
                          value={selectedField.appearance.fontSize}
                          onChange={(e) =>
                            updateField(selectedField.id, {
                              appearance: {
                                ...selectedField.appearance,
                                fontSize: Number(e.target.value),
                              },
                            })
                          }
                        />
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="field-font-color">Font Color</Label>
                        <Input
                          id="field-font-color"
                          type="color"
                          value={selectedField.appearance.fontColor}
                          onChange={(e) =>
                            updateField(selectedField.id, {
                              appearance: {
                                ...selectedField.appearance,
                                fontColor: e.target.value,
                              },
                            })
                          }
                        />
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="field-bg-color">Background Color</Label>
                        <Input
                          id="field-bg-color"
                          type="color"
                          value={selectedField.appearance.backgroundColor}
                          onChange={(e) =>
                            updateField(selectedField.id, {
                              appearance: {
                                ...selectedField.appearance,
                                backgroundColor: e.target.value,
                              },
                            })
                          }
                        />
                      </div>
                    </div>

                    {/* Field-specific options */}
                    {(selectedField.type === "select" ||
                      selectedField.type === "radio") && (
                      <>
                        <Separator />
                        <div className="space-y-3">
                          <h4 className="text-sm font-medium">Options</h4>
                          <Textarea
                            placeholder="Enter options, one per line"
                            value={selectedField.options?.map(opt => typeof opt === 'string' ? opt : opt.label).join("\n") || ""}
                            onChange={(e) =>
                              updateField(selectedField.id, {
                                options: e.target.value
                                  .split("\n")
                                  .filter((opt) => opt.trim())
                                  .map((opt) => ({ value: opt.trim(), label: opt.trim() })),
                              })
                            }
                          />
                        </div>
                      </>
                    )}
                  </div>
                </ScrollArea>
              </>
            ) : (
              <div className="flex items-center justify-center h-full text-center">
                <div>
                  <Edit className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
                  <p className="text-sm text-muted-foreground">
                    Select a field to edit its properties
                  </p>
                </div>
              </div>
            )}
          </div>
        </div>
      </CardContent>
    </div>
  );
}

"use client";

import type React from "react";

import { useCallback } from "react";
import {
  ContextMenu,
  ContextMenuContent,
  ContextMenuItem,
  ContextMenuSeparator,
  ContextMenuTrigger,
} from "@/components/ui/context-menu";
import {
  Co<PERSON>,
  Search,
  Bookmark,
  HighlighterIcon as Highlight,
} from "lucide-react";
import { toast } from "sonner";

interface PDFContextMenuProps {
  children: React.ReactNode;
  selectedText: string;
  pageNumber: number;
  onSearch?: (text: string) => void;
  onBookmark?: () => void;
  onHighlight?: (text: string) => void;
}

export default function PDFContextMenu({
  children,
  selectedText,
  pageNumber,
  onSearch,
  onBookmark,
  onHighlight,
}: PDFContextMenuProps) {


  const copyText = useCallback(async () => {
    if (!selectedText) return;

    try {
      await navigator.clipboard.writeText(selectedText);
      toast("Text copied", {
        description: `Copied "${selectedText.substring(0, 50)}${
          selectedText.length > 50 ? "..." : ""
        }" to clipboard.`,
      });
    } catch {
      toast.error("Copy failed", {
        description: "Unable to copy text to clipboard.",
      });
    }
  }, [selectedText]);

  const searchText = useCallback(() => {
    if (selectedText && onSearch) {
      onSearch(selectedText);
      toast("Searching", {
        description: `Searching for "${selectedText.substring(0, 30)}${
          selectedText.length > 30 ? "..." : ""
        }"`,
      });
    }
  }, [selectedText, onSearch]);

  const highlightText = useCallback(() => {
    if (selectedText && onHighlight) {
      onHighlight(selectedText);
      toast("Text highlighted", {
        description: "Selected text has been highlighted.",
      });
    }
  }, [selectedText, onHighlight]);

  return (
    <ContextMenu>
      <ContextMenuTrigger asChild>{children}</ContextMenuTrigger>
      <ContextMenuContent className="w-48">
        {selectedText && (
          <>
            <ContextMenuItem
              onClick={copyText}
              className="flex items-center gap-2"
            >
              <Copy className="h-4 w-4" />
              Copy Text
              <span className="ml-auto text-xs text-muted-foreground">
                Ctrl+C
              </span>
            </ContextMenuItem>

            <ContextMenuItem
              onClick={searchText}
              className="flex items-center gap-2"
            >
              <Search className="h-4 w-4" />
              Search for This
            </ContextMenuItem>

            <ContextMenuItem
              onClick={highlightText}
              className="flex items-center gap-2"
            >
              <Highlight className="h-4 w-4" />
              Highlight Text
            </ContextMenuItem>

            <ContextMenuSeparator />
          </>
        )}

        <ContextMenuItem
          onClick={onBookmark}
          className="flex items-center gap-2"
        >
          <Bookmark className="h-4 w-4" />
          Bookmark Page {pageNumber}
        </ContextMenuItem>

        <ContextMenuSeparator />

        <ContextMenuItem disabled className="text-xs text-muted-foreground">
          Page {pageNumber}
        </ContextMenuItem>
      </ContextMenuContent>
    </ContextMenu>
  );
}

"use client";

import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { 
  Smartphone, 
  Tablet, 
  Monitor, 
  CheckCircle, 
  AlertTriangle,
  Eye,
  Touch,
  Zap
} from 'lucide-react';
import { MOBILE_BREAKPOINTS, type MobileBreakpoint } from '@/test/mobile/mobile-testing-utils';

interface ResponsiveFeature {
  name: string;
  description: string;
  icon: React.ComponentType<{ className?: string }>;
  implemented: boolean;
  details: string[];
}

const RESPONSIVE_FEATURES: ResponsiveFeature[] = [
  {
    name: 'Touch Targets',
    description: 'All interactive elements meet 44px minimum touch target size',
    icon: Touch,
    implemented: true,
    details: [
      'Buttons use touch-target and touch-target-comfortable classes',
      'Input fields have minimum 44px height on mobile',
      'Navigation controls are 48px for comfortable interaction',
      'Checkbox and radio buttons scale to 24px on touch devices'
    ]
  },
  {
    name: 'Responsive Layout',
    description: 'Layout adapts seamlessly across all screen sizes',
    icon: Monitor,
    implemented: true,
    details: [
      'Mobile-first design approach',
      'Sidebar converts to overlay on mobile',
      'Desktop controls hidden on small screens',
      'Mobile navigation footer for essential controls'
    ]
  },
  {
    name: 'Performance Optimization',
    description: 'Optimized rendering and interactions for mobile devices',
    icon: Zap,
    implemented: true,
    details: [
      'Hardware acceleration with transform3d',
      'Optimized PDF rendering for mobile',
      'Layout shift prevention',
      'Reduced motion support for accessibility'
    ]
  },
  {
    name: 'Safe Area Support',
    description: 'Proper handling of device safe areas and notches',
    icon: Smartphone,
    implemented: true,
    details: [
      'Safe area inset support for modern devices',
      'Proper padding for status bars',
      'Notch-aware layout design',
      'Bottom safe area for home indicators'
    ]
  }
];

export default function MobileResponsiveDemo() {
  const [currentBreakpoint, setCurrentBreakpoint] = useState<MobileBreakpoint>(MOBILE_BREAKPOINTS.iphone6);
  const [viewportSize, setViewportSize] = useState({ width: 1024, height: 768 });

  useEffect(() => {
    const updateViewportSize = () => {
      setViewportSize({
        width: window.innerWidth,
        height: window.innerHeight
      });
    };

    updateViewportSize();
    window.addEventListener('resize', updateViewportSize);
    return () => window.removeEventListener('resize', updateViewportSize);
  }, []);

  const getCurrentBreakpointInfo = () => {
    const width = viewportSize.width;
    if (width < 375) return { name: 'Extra Small', icon: Smartphone, color: 'bg-red-500' };
    if (width < 414) return { name: 'Small Mobile', icon: Smartphone, color: 'bg-orange-500' };
    if (width < 768) return { name: 'Large Mobile', icon: Smartphone, color: 'bg-yellow-500' };
    if (width < 1024) return { name: 'Tablet', icon: Tablet, color: 'bg-blue-500' };
    return { name: 'Desktop', icon: Monitor, color: 'bg-green-500' };
  };

  const currentInfo = getCurrentBreakpointInfo();

  return (
    <div className="container mx-auto mobile-padding lg:p-6 space-y-6 safe-area-top safe-area-bottom">
      <div className="text-center space-y-4">
        <h1 className="text-2xl lg:text-3xl font-bold">Mobile Responsive PDF Viewer</h1>
        <p className="text-muted-foreground">
          Demonstrating mobile-first design and responsive features
        </p>
        
        {/* Current Viewport Info */}
        <Card className="max-w-md mx-auto">
          <CardContent className="mobile-padding lg:p-6">
            <div className="flex items-center justify-center space-x-3">
              <currentInfo.icon className="h-6 w-6" />
              <div className="text-center">
                <div className="font-semibold">{currentInfo.name}</div>
                <div className="text-sm text-muted-foreground">
                  {viewportSize.width} × {viewportSize.height}
                </div>
              </div>
              <Badge className={`${currentInfo.color} text-white`}>
                Current
              </Badge>
            </div>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="features" className="w-full">
        <TabsList className="grid w-full grid-cols-3 touch-target">
          <TabsTrigger value="features" className="touch-target">Features</TabsTrigger>
          <TabsTrigger value="breakpoints" className="touch-target">Breakpoints</TabsTrigger>
          <TabsTrigger value="testing" className="touch-target">Testing</TabsTrigger>
        </TabsList>

        <TabsContent value="features" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            {RESPONSIVE_FEATURES.map((feature) => (
              <Card key={feature.name}>
                <CardHeader className="mobile-padding lg:p-6">
                  <CardTitle className="flex items-center space-x-2">
                    <feature.icon className="h-5 w-5" />
                    <span>{feature.name}</span>
                    {feature.implemented ? (
                      <CheckCircle className="h-4 w-4 text-green-500" />
                    ) : (
                      <AlertTriangle className="h-4 w-4 text-yellow-500" />
                    )}
                  </CardTitle>
                  <CardDescription>{feature.description}</CardDescription>
                </CardHeader>
                <CardContent className="mobile-padding lg:p-6 pt-0">
                  <ul className="space-y-1 text-sm">
                    {feature.details.map((detail, index) => (
                      <li key={index} className="flex items-start space-x-2">
                        <CheckCircle className="h-3 w-3 text-green-500 mt-0.5 flex-shrink-0" />
                        <span>{detail}</span>
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="breakpoints" className="space-y-4">
          <div className="grid gap-4 sm:grid-cols-2 lg:grid-cols-3">
            {Object.values(MOBILE_BREAKPOINTS).map((breakpoint) => (
              <Card 
                key={breakpoint.name}
                className={`cursor-pointer transition-colors ${
                  currentBreakpoint.name === breakpoint.name 
                    ? 'ring-2 ring-primary' 
                    : 'hover:bg-muted/50'
                }`}
                onClick={() => setCurrentBreakpoint(breakpoint)}
              >
                <CardHeader className="mobile-padding lg:p-4">
                  <CardTitle className="text-base flex items-center space-x-2">
                    <Smartphone className="h-4 w-4" />
                    <span>{breakpoint.name}</span>
                  </CardTitle>
                  <CardDescription className="text-sm">
                    {breakpoint.width} × {breakpoint.height}
                  </CardDescription>
                </CardHeader>
                <CardContent className="mobile-padding lg:p-4 pt-0">
                  <div className="space-y-2 text-xs">
                    <div>
                      <span className="font-medium">DPR:</span> {breakpoint.devicePixelRatio}
                    </div>
                    <div className="text-muted-foreground">
                      {breakpoint.description}
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="testing" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Eye className="h-5 w-5" />
                <span>Mobile Testing Guidelines</span>
              </CardTitle>
              <CardDescription>
                How to test the mobile responsiveness of the PDF viewer
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-3">
                <h4 className="font-semibold">Manual Testing</h4>
                <ul className="space-y-2 text-sm">
                  <li className="flex items-start space-x-2">
                    <CheckCircle className="h-3 w-3 text-green-500 mt-0.5 flex-shrink-0" />
                    <span>Test on actual devices when possible</span>
                  </li>
                  <li className="flex items-start space-x-2">
                    <CheckCircle className="h-3 w-3 text-green-500 mt-0.5 flex-shrink-0" />
                    <span>Use browser dev tools to simulate different screen sizes</span>
                  </li>
                  <li className="flex items-start space-x-2">
                    <CheckCircle className="h-3 w-3 text-green-500 mt-0.5 flex-shrink-0" />
                    <span>Check touch target sizes with accessibility tools</span>
                  </li>
                  <li className="flex items-start space-x-2">
                    <CheckCircle className="h-3 w-3 text-green-500 mt-0.5 flex-shrink-0" />
                    <span>Verify no horizontal scrolling on mobile</span>
                  </li>
                </ul>
              </div>

              <div className="space-y-3">
                <h4 className="font-semibold">Automated Testing</h4>
                <ul className="space-y-2 text-sm">
                  <li className="flex items-start space-x-2">
                    <CheckCircle className="h-3 w-3 text-green-500 mt-0.5 flex-shrink-0" />
                    <span>Run mobile test suite: <code className="bg-muted px-1 rounded">npm test mobile</code></span>
                  </li>
                  <li className="flex items-start space-x-2">
                    <CheckCircle className="h-3 w-3 text-green-500 mt-0.5 flex-shrink-0" />
                    <span>Use mobile testing utilities for responsive checks</span>
                  </li>
                  <li className="flex items-start space-x-2">
                    <CheckCircle className="h-3 w-3 text-green-500 mt-0.5 flex-shrink-0" />
                    <span>Validate touch target accessibility requirements</span>
                  </li>
                </ul>
              </div>

              <div className="bg-muted/50 p-4 rounded-lg">
                <h5 className="font-medium mb-2">Key Test Cases</h5>
                <div className="grid gap-2 text-sm">
                  <div>✅ Touch targets ≥ 44px</div>
                  <div>✅ No horizontal scrolling</div>
                  <div>✅ Content fits viewport</div>
                  <div>✅ Safe area support</div>
                  <div>✅ Responsive navigation</div>
                  <div>✅ Performance optimization</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Demo Controls */}
      <Card>
        <CardHeader>
          <CardTitle>Interactive Demo</CardTitle>
          <CardDescription>
            Test the responsive features with these controls
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid gap-2 sm:grid-cols-2 lg:grid-cols-4">
            <Button 
              variant="outline" 
              className="touch-target-comfortable"
              onClick={() => window.open('/test-mobile', '_blank')}
            >
              <Smartphone className="h-4 w-4 mr-2" />
              Mobile View
            </Button>
            <Button 
              variant="outline" 
              className="touch-target-comfortable"
              onClick={() => window.open('/test-tablet', '_blank')}
            >
              <Tablet className="h-4 w-4 mr-2" />
              Tablet View
            </Button>
            <Button 
              variant="outline" 
              className="touch-target-comfortable"
              onClick={() => console.log('Touch test initiated')}
            >
              <Touch className="h-4 w-4 mr-2" />
              Touch Test
            </Button>
            <Button 
              variant="outline" 
              className="touch-target-comfortable"
              onClick={() => console.log('Performance test initiated')}
            >
              <Zap className="h-4 w-4 mr-2" />
              Performance
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

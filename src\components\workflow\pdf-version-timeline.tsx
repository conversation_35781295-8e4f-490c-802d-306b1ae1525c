"use client"

import { useState, useMemo } from "react"
import { Card, CardContent } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { <PERSON>roll<PERSON><PERSON> } from "@/components/ui/scroll-area"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import {
  GitBranch,
  GitCommit,
  GitMerge,
  Clock,
  User,
  Tag,
  Star,
  Lock,
  Eye,
  RotateCcw,
  Calendar,
  Filter,
} from "lucide-react"
import { cn } from "@/lib/utils"
import type { DocumentVersion, DocumentBranch } from "./pdf-version-control"

interface PDFVersionTimelineProps {
  versions: DocumentVersion[]
  branches: DocumentBranch[]
  currentVersion: string
  onVersionSelect: (versionId: string) => void
  onVersionRestore: (versionId: string) => void
}

type TimelineGrouping = "chronological" | "by-branch" | "by-author" | "by-type"

export default function PDFVersionTimeline({
  versions,
  branches,
  currentVersion,
  onVersionSelect,
  onVersionRestore,
}: PDFVersionTimelineProps) {
  const [grouping, setGrouping] = useState<TimelineGrouping>("chronological")
  const [selectedBranch, setSelectedBranch] = useState<string>("all")
  const [selectedAuthor, setSelectedAuthor] = useState<string>("all")
  const [timeRange, setTimeRange] = useState<string>("all")

  const authors = useMemo(() => {
    const authorSet = new Set(versions.map((v) => v.author.id))
    return Array.from(authorSet).map((id) => {
      const author = versions.find((v) => v.author.id === id)?.author
      return author!
    })
  }, [versions])

  const filteredVersions = useMemo(() => {
    return versions.filter((version) => {
      if (selectedBranch !== "all" && version.branchName !== selectedBranch) return false
      if (selectedAuthor !== "all" && version.author.id !== selectedAuthor) return false

      if (timeRange !== "all") {
        const now = new Date()
        const versionDate = version.timestamp
        const daysDiff = (now.getTime() - versionDate.getTime()) / (1000 * 60 * 60 * 24)

        switch (timeRange) {
          case "7d":
            if (daysDiff > 7) return false
            break
          case "30d":
            if (daysDiff > 30) return false
            break
          case "90d":
            if (daysDiff > 90) return false
            break
        }
      }

      return true
    })
  }, [versions, selectedBranch, selectedAuthor, timeRange])

  const groupedVersions = useMemo(() => {
    switch (grouping) {
      case "by-branch":
        return filteredVersions.reduce(
          (acc, version) => {
            const branch = version.branchName || "main"
            if (!acc[branch]) acc[branch] = []
            acc[branch].push(version)
            return acc
          },
          {} as Record<string, DocumentVersion[]>,
        )

      case "by-author":
        return filteredVersions.reduce(
          (acc, version) => {
            const author = version.author.name
            if (!acc[author]) acc[author] = []
            acc[author].push(version)
            return acc
          },
          {} as Record<string, DocumentVersion[]>,
        )

      case "by-type":
        return filteredVersions.reduce(
          (acc, version) => {
            const type = version.isMajor ? "Major Releases" : "Minor Updates"
            if (!acc[type]) acc[type] = []
            acc[type].push(version)
            return acc
          },
          {} as Record<string, DocumentVersion[]>,
        )

      default: // chronological
        return { Timeline: filteredVersions.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime()) }
    }
  }, [filteredVersions, grouping])

  const renderVersionNode = (version: DocumentVersion, isLast = false) => (
    <div key={version.id} className="flex gap-4 pb-6">
      {/* Timeline line */}
      <div className="flex flex-col items-center">
        <div
          className={cn(
            "w-3 h-3 rounded-full border-2 bg-background",
            version.id === currentVersion ? "border-primary bg-primary" : "border-muted-foreground",
            version.isMajor && "w-4 h-4",
          )}
        >
          {version.isMajor && <Star className="w-2 h-2 text-white" />}
        </div>
        {!isLast && <div className="w-px h-full bg-border mt-2" />}
      </div>

      {/* Version card */}
      <Card className={cn("flex-1 mb-4", version.id === currentVersion && "ring-2 ring-primary")}>
        <CardContent className="p-4">
          <div className="flex items-start justify-between mb-3">
            <div className="flex items-center gap-3">
              <Avatar className="h-8 w-8">
                <AvatarImage src={version.author.avatar || "/placeholder.svg"} />
                <AvatarFallback>
                  {version.author.name
                    .split(" ")
                    .map((n) => n[0])
                    .join("")}
                </AvatarFallback>
              </Avatar>
              <div>
                <div className="flex items-center gap-2">
                  <span className="font-semibold">{version.version}</span>
                  {version.isMajor && <Badge variant="default">Major</Badge>}
                  {version.isLocked && <Lock className="h-3 w-3" />}
                  {version.id === currentVersion && <Badge variant="outline">Current</Badge>}
                </div>
                <div className="flex items-center gap-2 text-xs text-muted-foreground">
                  <User className="h-3 w-3" />
                  <span>{version.author.name}</span>
                  <Clock className="h-3 w-3 ml-2" />
                  <span>{version.timestamp.toLocaleString()}</span>
                </div>
              </div>
            </div>
            <div className="flex gap-1">
              <Button
                size="sm"
                variant="ghost"
                onClick={() => onVersionSelect(version.id)}
                disabled={version.id === currentVersion}
              >
                <Eye className="h-4 w-4" />
              </Button>
              <Button
                size="sm"
                variant="ghost"
                onClick={() => onVersionRestore(version.id)}
                disabled={version.id === currentVersion || version.isLocked}
              >
                <RotateCcw className="h-4 w-4" />
              </Button>
            </div>
          </div>

          <h4 className="font-medium mb-1">{version.title}</h4>
          <p className="text-sm text-muted-foreground mb-3">{version.description}</p>

          {/* Metadata */}
          <div className="flex items-center gap-4 text-xs text-muted-foreground mb-3">
            <span>{version.metadata.pageCount} pages</span>
            <span>{version.metadata.annotations} annotations</span>
            <span>{version.metadata.formFields} form fields</span>
            <span>{Math.round(version.size / 1024)} KB</span>
          </div>

          {/* Changes summary */}
          {version.changes.length > 0 && (
            <div className="mb-3">
              <div className="flex items-center gap-2 mb-2">
                <GitCommit className="h-3 w-3" />
                <span className="text-xs font-medium">{version.changes.length} changes</span>
              </div>
              <div className="space-y-1">
                {version.changes.slice(0, 3).map((change) => (
                  <div key={change.id} className="flex items-center gap-2 text-xs">
                    <Badge
                      variant={
                        change.type === "added"
                          ? "default"
                          : change.type === "modified"
                            ? "secondary"
                            : change.type === "deleted"
                              ? "destructive"
                              : "outline"
                      }
                      className="text-xs px-1 py-0"
                    >
                      {change.type}
                    </Badge>
                    <span className="text-muted-foreground">{change.description}</span>
                  </div>
                ))}
                {version.changes.length > 3 && (
                  <div className="text-xs text-muted-foreground">+{version.changes.length - 3} more changes</div>
                )}
              </div>
            </div>
          )}

          {/* Tags */}
          {version.tags.length > 0 && (
            <div className="flex gap-1 mb-3">
              {version.tags.map((tag) => (
                <Badge key={tag} variant="secondary" className="text-xs">
                  <Tag className="h-2 w-2 mr-1" />
                  {tag}
                </Badge>
              ))}
            </div>
          )}

          {/* Branch info */}
          {version.branchName && (
            <div className="flex items-center gap-2 text-xs text-muted-foreground">
              <GitBranch className="h-3 w-3" />
              <span>{version.branchName}</span>
              {version.mergeInfo && (
                <>
                  <GitMerge className="h-3 w-3 ml-2" />
                  <span>Merged from {version.mergeInfo.sourceBranch}</span>
                </>
              )}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )

  return (
    <div className="h-full flex flex-col">
      {/* Filters */}
      <div className="p-4 border-b space-y-4">
        <div className="flex items-center gap-2">
          <Filter className="h-4 w-4" />
          <span className="font-medium">Timeline Filters</span>
        </div>

        <div className="grid grid-cols-2 gap-4">
          <div>
            <label className="text-sm font-medium">Group by</label>
            <Select value={grouping} onValueChange={(value: TimelineGrouping) => setGrouping(value)}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="chronological">
                  <div className="flex items-center gap-2">
                    <Clock className="h-4 w-4" />
                    Chronological
                  </div>
                </SelectItem>
                <SelectItem value="by-branch">
                  <div className="flex items-center gap-2">
                    <GitBranch className="h-4 w-4" />
                    By Branch
                  </div>
                </SelectItem>
                <SelectItem value="by-author">
                  <div className="flex items-center gap-2">
                    <User className="h-4 w-4" />
                    By Author
                  </div>
                </SelectItem>
                <SelectItem value="by-type">
                  <div className="flex items-center gap-2">
                    <Star className="h-4 w-4" />
                    By Type
                  </div>
                </SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div>
            <label className="text-sm font-medium">Time Range</label>
            <Select value={timeRange} onValueChange={setTimeRange}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Time</SelectItem>
                <SelectItem value="7d">Last 7 days</SelectItem>
                <SelectItem value="30d">Last 30 days</SelectItem>
                <SelectItem value="90d">Last 90 days</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div>
            <label className="text-sm font-medium">Branch</label>
            <Select value={selectedBranch} onValueChange={setSelectedBranch}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Branches</SelectItem>
                {branches.map((branch) => (
                  <SelectItem key={branch.id} value={branch.name}>
                    {branch.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div>
            <label className="text-sm font-medium">Author</label>
            <Select value={selectedAuthor} onValueChange={setSelectedAuthor}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Authors</SelectItem>
                {authors.map((author) => (
                  <SelectItem key={author.id} value={author.id}>
                    {author.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>
      </div>

      {/* Timeline */}
      <ScrollArea className="flex-1">
        <div className="p-4">
          {Object.entries(groupedVersions).map(([groupName, groupVersions]) => (
            <div key={groupName} className="mb-8">
              {grouping !== "chronological" && (
                <div className="flex items-center gap-2 mb-6 pb-2 border-b">
                  <h3 className="font-semibold">{groupName}</h3>
                  <Badge variant="outline">{groupVersions.length} versions</Badge>
                </div>
              )}

              <div className="space-y-0">
                {groupVersions
                  .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())
                  .map((version, index) => renderVersionNode(version, index === groupVersions.length - 1))}
              </div>
            </div>
          ))}

          {filteredVersions.length === 0 && (
            <div className="text-center py-8">
              <Calendar className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
              <h3 className="font-medium mb-2">No versions found</h3>
              <p className="text-sm text-muted-foreground">Try adjusting your filters to see more versions</p>
            </div>
          )}
        </div>
      </ScrollArea>
    </div>
  )
}

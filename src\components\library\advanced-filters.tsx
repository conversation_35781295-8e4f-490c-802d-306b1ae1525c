"use client";

import React, { useState, useCallback } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Slider } from '@/components/ui/slider';
import { Checkbox } from '@/components/ui/checkbox';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Separator } from '@/components/ui/separator';
import {
  Filter,
  X,
  Calendar as CalendarIcon,
  FileSize,
  Clock,
  Star,
  Pin,
  BookOpen,
  FileText,
  User,
  Tag,
  Folder,
  RotateCcw
} from 'lucide-react';
import { format } from 'date-fns';
import { cn } from '@/lib/utils';
import type { DocumentSearchQuery, DocumentInstance } from '@/lib/types/pdf';
import { formatFileSize } from '@/lib/types/pdf';

interface AdvancedFiltersProps {
  documents: DocumentInstance[];
  onFilterChange: (filters: DocumentSearchQuery) => void;
  className?: string;
}

interface FilterState {
  text: string;
  tags: string[];
  categories: string[];
  authors: string[];
  dateRange: { start: Date | null; end: Date | null };
  sizeRange: { min: number; max: number };
  pageCountRange: { min: number; max: number };
  hasAnnotations: boolean | null;
  hasBookmarks: boolean | null;
  hasFormFields: boolean | null;
  isFavorite: boolean | null;
  isPinned: boolean | null;
  rating: number | null;
}

export default function AdvancedFilters({
  documents,
  onFilterChange,
  className
}: AdvancedFiltersProps) {
  const [isExpanded, setIsExpanded] = useState(false);
  const [filters, setFilters] = useState<FilterState>({
    text: '',
    tags: [],
    categories: [],
    authors: [],
    dateRange: { start: null, end: null },
    sizeRange: { min: 0, max: 100 * 1024 * 1024 }, // 100MB
    pageCountRange: { min: 1, max: 1000 },
    hasAnnotations: null,
    hasBookmarks: null,
    hasFormFields: null,
    isFavorite: null,
    isPinned: null,
    rating: null
  });

  // Extract available options from documents
  const availableTags = React.useMemo(() => {
    const tags = new Set<string>();
    documents.forEach(doc => doc.metadata.tags.forEach(tag => tags.add(tag)));
    return Array.from(tags).sort();
  }, [documents]);

  const availableCategories = React.useMemo(() => {
    const categories = new Set<string>();
    documents.forEach(doc => doc.metadata.categories.forEach(cat => categories.add(cat)));
    return Array.from(categories).sort();
  }, [documents]);

  const availableAuthors = React.useMemo(() => {
    const authors = new Set<string>();
    documents.forEach(doc => {
      if (doc.metadata.author) authors.add(doc.metadata.author);
    });
    return Array.from(authors).sort();
  }, [documents]);

  const maxFileSize = React.useMemo(() => {
    return Math.max(...documents.map(doc => doc.metadata.fileSize), 100 * 1024 * 1024);
  }, [documents]);

  const maxPageCount = React.useMemo(() => {
    return Math.max(...documents.map(doc => doc.metadata.pageCount), 1000);
  }, [documents]);

  // Update filters and notify parent
  const updateFilters = useCallback((newFilters: Partial<FilterState>) => {
    const updatedFilters = { ...filters, ...newFilters };
    setFilters(updatedFilters);

    // Convert to DocumentSearchQuery format
    const searchQuery: DocumentSearchQuery = {
      text: updatedFilters.text || undefined,
      tags: updatedFilters.tags.length > 0 ? updatedFilters.tags : undefined,
      categories: updatedFilters.categories.length > 0 ? updatedFilters.categories : undefined,
      dateRange: updatedFilters.dateRange.start && updatedFilters.dateRange.end 
        ? { start: updatedFilters.dateRange.start, end: updatedFilters.dateRange.end }
        : undefined,
      sizeRange: updatedFilters.sizeRange.min > 0 || updatedFilters.sizeRange.max < maxFileSize
        ? updatedFilters.sizeRange
        : undefined,
      pageCountRange: updatedFilters.pageCountRange.min > 1 || updatedFilters.pageCountRange.max < maxPageCount
        ? updatedFilters.pageCountRange
        : undefined,
      hasAnnotations: updatedFilters.hasAnnotations,
      hasBookmarks: updatedFilters.hasBookmarks,
      hasFormFields: updatedFilters.hasFormFields,
      isFavorite: updatedFilters.isFavorite,
      rating: updatedFilters.rating
    };

    onFilterChange(searchQuery);
  }, [filters, onFilterChange, maxFileSize, maxPageCount]);

  // Reset all filters
  const resetFilters = useCallback(() => {
    const resetState: FilterState = {
      text: '',
      tags: [],
      categories: [],
      authors: [],
      dateRange: { start: null, end: null },
      sizeRange: { min: 0, max: maxFileSize },
      pageCountRange: { min: 1, max: maxPageCount },
      hasAnnotations: null,
      hasBookmarks: null,
      hasFormFields: null,
      isFavorite: null,
      isPinned: null,
      rating: null
    };
    setFilters(resetState);
    onFilterChange({});
  }, [maxFileSize, maxPageCount, onFilterChange]);

  // Helper functions
  const addTag = (tag: string) => {
    if (!filters.tags.includes(tag)) {
      updateFilters({ tags: [...filters.tags, tag] });
    }
  };

  const removeTag = (tag: string) => {
    updateFilters({ tags: filters.tags.filter(t => t !== tag) });
  };

  const addCategory = (category: string) => {
    if (!filters.categories.includes(category)) {
      updateFilters({ categories: [...filters.categories, category] });
    }
  };

  const removeCategory = (category: string) => {
    updateFilters({ categories: filters.categories.filter(c => c !== category) });
  };

  const addAuthor = (author: string) => {
    if (!filters.authors.includes(author)) {
      updateFilters({ authors: [...filters.authors, author] });
    }
  };

  const removeAuthor = (author: string) => {
    updateFilters({ authors: filters.authors.filter(a => a !== author) });
  };

  // Count active filters
  const activeFilterCount = React.useMemo(() => {
    let count = 0;
    if (filters.text) count++;
    if (filters.tags.length > 0) count++;
    if (filters.categories.length > 0) count++;
    if (filters.authors.length > 0) count++;
    if (filters.dateRange.start || filters.dateRange.end) count++;
    if (filters.sizeRange.min > 0 || filters.sizeRange.max < maxFileSize) count++;
    if (filters.pageCountRange.min > 1 || filters.pageCountRange.max < maxPageCount) count++;
    if (filters.hasAnnotations !== null) count++;
    if (filters.hasBookmarks !== null) count++;
    if (filters.hasFormFields !== null) count++;
    if (filters.isFavorite !== null) count++;
    if (filters.isPinned !== null) count++;
    if (filters.rating !== null) count++;
    return count;
  }, [filters, maxFileSize, maxPageCount]);

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Filter className="h-5 w-5" />
            Advanced Filters
            {activeFilterCount > 0 && (
              <Badge variant="secondary">{activeFilterCount}</Badge>
            )}
          </CardTitle>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={resetFilters}
              disabled={activeFilterCount === 0}
            >
              <RotateCcw className="h-4 w-4 mr-1" />
              Reset
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsExpanded(!isExpanded)}
            >
              {isExpanded ? 'Collapse' : 'Expand'}
            </Button>
          </div>
        </div>
      </CardHeader>

      {isExpanded && (
        <CardContent className="space-y-6">
          {/* Text Search */}
          <div>
            <Label>Search Text</Label>
            <Input
              placeholder="Search in title, author, description..."
              value={filters.text}
              onChange={(e) => updateFilters({ text: e.target.value })}
              className="mt-1"
            />
          </div>

          <Separator />

          {/* Tags Filter */}
          <div>
            <Label>Tags</Label>
            <Select onValueChange={addTag}>
              <SelectTrigger className="mt-1">
                <SelectValue placeholder="Select tags..." />
              </SelectTrigger>
              <SelectContent>
                {availableTags.map(tag => (
                  <SelectItem key={tag} value={tag} disabled={filters.tags.includes(tag)}>
                    <div className="flex items-center gap-2">
                      <Tag className="h-4 w-4" />
                      {tag}
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {filters.tags.length > 0 && (
              <div className="flex flex-wrap gap-1 mt-2">
                {filters.tags.map(tag => (
                  <Badge key={tag} variant="secondary" className="flex items-center gap-1">
                    {tag}
                    <X
                      className="h-3 w-3 cursor-pointer"
                      onClick={() => removeTag(tag)}
                    />
                  </Badge>
                ))}
              </div>
            )}
          </div>

          {/* Categories Filter */}
          <div>
            <Label>Categories</Label>
            <Select onValueChange={addCategory}>
              <SelectTrigger className="mt-1">
                <SelectValue placeholder="Select categories..." />
              </SelectTrigger>
              <SelectContent>
                {availableCategories.map(category => (
                  <SelectItem key={category} value={category} disabled={filters.categories.includes(category)}>
                    <div className="flex items-center gap-2">
                      <Folder className="h-4 w-4" />
                      {category}
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {filters.categories.length > 0 && (
              <div className="flex flex-wrap gap-1 mt-2">
                {filters.categories.map(category => (
                  <Badge key={category} variant="outline" className="flex items-center gap-1">
                    <Folder className="h-3 w-3" />
                    {category}
                    <X
                      className="h-3 w-3 cursor-pointer"
                      onClick={() => removeCategory(category)}
                    />
                  </Badge>
                ))}
              </div>
            )}
          </div>

          {/* Authors Filter */}
          {availableAuthors.length > 0 && (
            <div>
              <Label>Authors</Label>
              <Select onValueChange={addAuthor}>
                <SelectTrigger className="mt-1">
                  <SelectValue placeholder="Select authors..." />
                </SelectTrigger>
                <SelectContent>
                  {availableAuthors.map(author => (
                    <SelectItem key={author} value={author} disabled={filters.authors.includes(author)}>
                      <div className="flex items-center gap-2">
                        <User className="h-4 w-4" />
                        {author}
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {filters.authors.length > 0 && (
                <div className="flex flex-wrap gap-1 mt-2">
                  {filters.authors.map(author => (
                    <Badge key={author} variant="outline" className="flex items-center gap-1">
                      <User className="h-3 w-3" />
                      {author}
                      <X
                        className="h-3 w-3 cursor-pointer"
                        onClick={() => removeAuthor(author)}
                      />
                    </Badge>
                  ))}
                </div>
              )}
            </div>
          )}

          <Separator />

          {/* Date Range */}
          <div>
            <Label>Date Range</Label>
            <div className="flex gap-2 mt-1">
              <Popover>
                <PopoverTrigger asChild>
                  <Button variant="outline" className="flex-1 justify-start">
                    <CalendarIcon className="h-4 w-4 mr-2" />
                    {filters.dateRange.start ? format(filters.dateRange.start, 'PPP') : 'Start date'}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0">
                  <Calendar
                    mode="single"
                    selected={filters.dateRange.start || undefined}
                    onSelect={(date) => updateFilters({ 
                      dateRange: { ...filters.dateRange, start: date || null }
                    })}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
              <Popover>
                <PopoverTrigger asChild>
                  <Button variant="outline" className="flex-1 justify-start">
                    <CalendarIcon className="h-4 w-4 mr-2" />
                    {filters.dateRange.end ? format(filters.dateRange.end, 'PPP') : 'End date'}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0">
                  <Calendar
                    mode="single"
                    selected={filters.dateRange.end || undefined}
                    onSelect={(date) => updateFilters({ 
                      dateRange: { ...filters.dateRange, end: date || null }
                    })}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
            </div>
          </div>

          {/* File Size Range */}
          <div>
            <Label>File Size Range</Label>
            <div className="mt-2">
              <Slider
                value={[filters.sizeRange.min, filters.sizeRange.max]}
                onValueChange={([min, max]) => updateFilters({ sizeRange: { min, max } })}
                max={maxFileSize}
                step={1024 * 1024} // 1MB steps
                className="w-full"
              />
              <div className="flex justify-between text-xs text-muted-foreground mt-1">
                <span>{formatFileSize(filters.sizeRange.min)}</span>
                <span>{formatFileSize(filters.sizeRange.max)}</span>
              </div>
            </div>
          </div>

          {/* Page Count Range */}
          <div>
            <Label>Page Count Range</Label>
            <div className="mt-2">
              <Slider
                value={[filters.pageCountRange.min, filters.pageCountRange.max]}
                onValueChange={([min, max]) => updateFilters({ pageCountRange: { min, max } })}
                max={maxPageCount}
                step={1}
                className="w-full"
              />
              <div className="flex justify-between text-xs text-muted-foreground mt-1">
                <span>{filters.pageCountRange.min} pages</span>
                <span>{filters.pageCountRange.max} pages</span>
              </div>
            </div>
          </div>

          <Separator />

          {/* Boolean Filters */}
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-3">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="has-annotations"
                  checked={filters.hasAnnotations === true}
                  onCheckedChange={(checked) => 
                    updateFilters({ hasAnnotations: checked ? true : null })
                  }
                />
                <Label htmlFor="has-annotations" className="flex items-center gap-2">
                  <FileText className="h-4 w-4" />
                  Has Annotations
                </Label>
              </div>

              <div className="flex items-center space-x-2">
                <Checkbox
                  id="has-bookmarks"
                  checked={filters.hasBookmarks === true}
                  onCheckedChange={(checked) => 
                    updateFilters({ hasBookmarks: checked ? true : null })
                  }
                />
                <Label htmlFor="has-bookmarks" className="flex items-center gap-2">
                  <BookOpen className="h-4 w-4" />
                  Has Bookmarks
                </Label>
              </div>

              <div className="flex items-center space-x-2">
                <Checkbox
                  id="has-form-fields"
                  checked={filters.hasFormFields === true}
                  onCheckedChange={(checked) => 
                    updateFilters({ hasFormFields: checked ? true : null })
                  }
                />
                <Label htmlFor="has-form-fields" className="flex items-center gap-2">
                  <FileText className="h-4 w-4" />
                  Has Form Fields
                </Label>
              </div>
            </div>

            <div className="space-y-3">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="is-favorite"
                  checked={filters.isFavorite === true}
                  onCheckedChange={(checked) => 
                    updateFilters({ isFavorite: checked ? true : null })
                  }
                />
                <Label htmlFor="is-favorite" className="flex items-center gap-2">
                  <Star className="h-4 w-4" />
                  Favorites Only
                </Label>
              </div>

              <div className="flex items-center space-x-2">
                <Checkbox
                  id="is-pinned"
                  checked={filters.isPinned === true}
                  onCheckedChange={(checked) => 
                    updateFilters({ isPinned: checked ? true : null })
                  }
                />
                <Label htmlFor="is-pinned" className="flex items-center gap-2">
                  <Pin className="h-4 w-4" />
                  Pinned Only
                </Label>
              </div>
            </div>
          </div>
        </CardContent>
      )}
    </Card>
  );
}

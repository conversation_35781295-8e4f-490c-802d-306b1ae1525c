"use client";

import { useState, useCallback } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Eye,
  Download,
  Copy,
  Search,
  Zap,
  FileText,
  ImageIcon,
} from "lucide-react";
import { toast } from "sonner";
import { extractPDFDocument, type PDFDocument } from "@/lib/types/pdf";

export interface OCRResult {
  id: string;
  pageNumber: number;
  text: string;
  confidence: number;
  boundingBox: { x: number; y: number; width: number; height: number };
  language: string;
  words: Array<{
    text: string;
    confidence: number;
    boundingBox: { x: number; y: number; width: number; height: number };
  }>;
  processingTime: number;
}

interface PDFOCREngineProps {
  pdfDocument: PDFDocument;
  numPages: number;
  currentPage?: number;
  onTextExtracted?: (results: OCRResult[]) => void;
  onOCRResult?: (result: OCRResult[]) => void; // Enhanced callback for compatibility
  className?: string;
  // Enhanced configuration options
  defaultLanguage?: string;
  enableAutoDetect?: boolean;
  enableImageEnhancement?: boolean;
  processingMode?: 'fast' | 'accurate' | 'balanced';
  batchSize?: number;
}

const SUPPORTED_LANGUAGES = [
  { code: "eng", name: "English" },
  { code: "spa", name: "Spanish" },
  { code: "fra", name: "French" },
  { code: "deu", name: "German" },
  { code: "ita", name: "Italian" },
  { code: "por", name: "Portuguese" },
  { code: "rus", name: "Russian" },
  { code: "chi_sim", name: "Chinese (Simplified)" },
  { code: "chi_tra", name: "Chinese (Traditional)" },
  { code: "jpn", name: "Japanese" },
  { code: "kor", name: "Korean" },
  { code: "ara", name: "Arabic" },
  { code: "hin", name: "Hindi" },
];

export default function PDFOCREngine({
  pdfDocument,
  numPages,
  onTextExtracted,
  onOCRResult,
  defaultLanguage = "eng",
  enableAutoDetect = false,
  enableImageEnhancement = true,
}: PDFOCREngineProps) {
  const [isProcessing, setIsProcessing] = useState(false);
  const [progress, setProgress] = useState(0);
  const [ocrResults, setOCRResults] = useState<OCRResult[]>([]);
  const [selectedLanguage, setSelectedLanguage] = useState(defaultLanguage);
  const [selectedPages, setSelectedPages] = useState<number[]>([]);
  const [selectAll, setSelectAll] = useState(true);
  const [enhanceImages, setEnhanceImages] = useState(enableImageEnhancement);
  const [autoDetectLanguage, setAutoDetectLanguage] = useState(enableAutoDetect);

  const processOCR = useCallback(async () => {
    if (!pdfDocument) return;

    setIsProcessing(true);
    setProgress(0);
    const results: OCRResult[] = [];

    try {
      const doc = extractPDFDocument(pdfDocument);
      if (!doc) {
        throw new Error("Invalid PDF document");
      }

      const pagesToProcess = selectAll
        ? Array.from({ length: numPages }, (_, i) => i + 1)
        : selectedPages;

      for (let i = 0; i < pagesToProcess.length; i++) {
        const pageNum = pagesToProcess[i];
        const startTime = Date.now();

        try {
          const page = await doc.getPage(pageNum);
          const viewport = page.getViewport({ scale: 2.0 }); // Higher scale for better OCR

          const canvas = document.createElement("canvas");
          const context = canvas.getContext("2d");
          if (!context) {
            throw new Error("Failed to get canvas context");
          }
          canvas.width = viewport.width;
          canvas.height = viewport.height;

          await page.render({
            canvasContext: context,
            viewport: viewport,
            canvas: canvas,
          }).promise;

          // Convert canvas to image data
          const imageData = canvas.toDataURL("image/png");

          // Simulate OCR processing (in real implementation, use Tesseract.js or similar)
          const mockOCRResult = await simulateOCR(
            imageData,
            selectedLanguage,
            enhanceImages,
            autoDetectLanguage
          );

          const result: OCRResult = {
            id: `ocr-${pageNum}-${Date.now()}`,
            pageNumber: pageNum,
            text: mockOCRResult.text,
            confidence: mockOCRResult.confidence,
            boundingBox: { x: 0, y: 0, width: viewport.width, height: viewport.height },
            language: mockOCRResult.detectedLanguage || selectedLanguage,
            words: mockOCRResult.words,
            processingTime: Date.now() - startTime,
          };

          results.push(result);
          setProgress(((i + 1) / pagesToProcess.length) * 100);

          // Update results incrementally
          setOCRResults([...results]);
        } catch (pageError) {
          console.error(`OCR failed for page ${pageNum}:`, pageError);
          toast(`OCR failed for page ${pageNum}`, {
            description: "Continuing with remaining pages...",
          });
        }
      }

      onTextExtracted?.(results);
      onOCRResult?.(results); // Enhanced callback for compatibility
      toast(`OCR completed: Processed ${results.length} pages successfully`);
    } catch (error) {
      console.error("OCR processing failed:", error);
      toast("OCR failed: An error occurred during text recognition");
    } finally {
      setIsProcessing(false);
    }
  }, [
    pdfDocument,
    numPages,
    selectedLanguage,
    selectedPages,
    selectAll,
    enhanceImages,
    autoDetectLanguage,
    onTextExtracted,
    onOCRResult,
  ]);

  // Simulate OCR processing (replace with real OCR library)
  const simulateOCR = async (
    imageData: string,
    language: string,
    enhance: boolean,
    autoDetect: boolean
  ) => {
    // Simulate processing delay
    await new Promise((resolve) =>
      setTimeout(resolve, 1000 + Math.random() * 2000)
    );

    // Mock OCR results
    const mockTexts = [
      "This is sample text extracted from the PDF page using OCR technology.",
      "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.",
      "The quick brown fox jumps over the lazy dog. This pangram contains every letter of the alphabet.",
      "OCR (Optical Character Recognition) is a technology that converts different types of documents into editable and searchable data.",
    ];

    const mockText = mockTexts[Math.floor(Math.random() * mockTexts.length)];
    const words = mockText.split(" ").map((word, index) => ({
      text: word,
      confidence: 0.85 + Math.random() * 0.15,
      boundingBox: { x: index * 50, y: 100, width: word.length * 8, height: 20 },
    }));

    return {
      text: mockText,
      confidence: 0.85 + Math.random() * 0.15,
      words,
      detectedLanguage: autoDetect
        ? ["eng", "spa", "fra"][Math.floor(Math.random() * 3)]
        : null,
    };
  };

  const togglePageSelection = (pageNum: number) => {
    setSelectedPages((prev) =>
      prev.includes(pageNum)
        ? prev.filter((p) => p !== pageNum)
        : [...prev, pageNum]
    );
  };

  const exportOCRResults = (format: "txt" | "json" | "csv") => {
    if (ocrResults.length === 0) return;

    let content = "";
    let filename = "";
    let mimeType = "";

    switch (format) {
      case "txt":
        content = ocrResults
          .map(
            (result) => `=== Page ${result.pageNumber} ===\n${result.text}\n\n`
          )
          .join("");
        filename = "ocr-results.txt";
        mimeType = "text/plain";
        break;

      case "json":
        content = JSON.stringify(ocrResults, null, 2);
        filename = "ocr-results.json";
        mimeType = "application/json";
        break;

      case "csv":
        const headers = [
          "Page",
          "Text",
          "Confidence",
          "Language",
          "Processing Time (ms)",
        ];
        const rows = ocrResults.map((result) => [
          result.pageNumber,
          `"${result.text.replace(/"/g, '""')}"`,
          result.confidence.toFixed(3),
          result.language,
          result.processingTime,
        ]);
        content = [headers.join(","), ...rows.map((row) => row.join(","))].join(
          "\n"
        );
        filename = "ocr-results.csv";
        mimeType = "text/csv";
        break;
    }

    const blob = new Blob([content], { type: mimeType });
    const url = URL.createObjectURL(blob);
    const link = document.createElement("a");
    link.href = url;
    link.download = filename;
    link.click();
    URL.revokeObjectURL(url);

    toast(`Export successful: OCR results exported as ${format.toUpperCase()}`);
  };

  const copyAllText = async () => {
    const allText = ocrResults.map((result) => result.text).join("\n\n");
    try {
      await navigator.clipboard.writeText(allText);
      toast("Text copied: All OCR text copied to clipboard");
    } catch {
      toast("Copy failed: Unable to copy text to clipboard");
    }
  };

  const averageConfidence =
    ocrResults.length > 0
      ? ocrResults.reduce((sum, result) => sum + result.confidence, 0) /
        ocrResults.length
      : 0;

  const totalProcessingTime = ocrResults.reduce(
    (sum, result) => sum + result.processingTime,
    0
  );

  return (
    <Card className="h-full flex flex-col">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Eye className="h-5 w-5" />
          OCR Text Recognition
        </CardTitle>
        <CardDescription>
          Extract text from PDF pages using optical character recognition
        </CardDescription>
      </CardHeader>

      <CardContent className="flex-1 overflow-hidden space-y-4">
        {/* OCR Settings */}
        <div className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="text-sm font-medium">Language</label>
              <Select
                value={selectedLanguage}
                onValueChange={setSelectedLanguage}
                disabled={autoDetectLanguage}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {SUPPORTED_LANGUAGES.map((lang) => (
                    <SelectItem key={lang.code} value={lang.code}>
                      {lang.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="auto-detect"
                  checked={autoDetectLanguage}
                  onCheckedChange={(checked) =>
                    setAutoDetectLanguage(checked as boolean)
                  }
                />
                <label htmlFor="auto-detect" className="text-sm">
                  Auto-detect language
                </label>
              </div>

              <div className="flex items-center space-x-2">
                <Checkbox
                  id="enhance"
                  checked={enhanceImages}
                  onCheckedChange={(checked) =>
                    setEnhanceImages(checked as boolean)
                  }
                />
                <label htmlFor="enhance" className="text-sm">
                  Enhance image quality
                </label>
              </div>
            </div>
          </div>

          {/* Page Selection */}
          <div>
            <div className="flex items-center justify-between mb-2">
              <label className="text-sm font-medium">Pages to Process</label>
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="select-all"
                  checked={selectAll}
                  onCheckedChange={(checked) =>
                    setSelectAll(checked as boolean)
                  }
                />
                <label htmlFor="select-all" className="text-sm">
                  All pages
                </label>
              </div>
            </div>

            {!selectAll && (
              <div className="grid grid-cols-10 gap-1 max-h-32 overflow-y-auto">
                {Array.from({ length: numPages }, (_, i) => i + 1).map(
                  (pageNum) => (
                    <Button
                      key={pageNum}
                      variant={
                        selectedPages.includes(pageNum) ? "default" : "outline"
                      }
                      size="sm"
                      className="h-8 text-xs"
                      onClick={() => togglePageSelection(pageNum)}
                    >
                      {pageNum}
                    </Button>
                  )
                )}
              </div>
            )}
          </div>

          {/* Process Button */}
          <Button
            onClick={processOCR}
            disabled={
              isProcessing || (!selectAll && selectedPages.length === 0)
            }
            className="w-full"
          >
            <Zap className="h-4 w-4 mr-2" />
            {isProcessing ? "Processing..." : "Start OCR"}
          </Button>

          {/* Progress */}
          {isProcessing && (
            <div className="space-y-2">
              <Progress value={progress} className="w-full" />
              <p className="text-sm text-muted-foreground text-center">
                Processing... {Math.round(progress)}%
              </p>
            </div>
          )}
        </div>

        {/* Results Summary */}
        {ocrResults.length > 0 && (
          <div className="space-y-4">
            <div className="grid grid-cols-3 gap-4 p-3 bg-muted/50 rounded-lg">
              <div className="text-center">
                <div className="text-lg font-bold">{ocrResults.length}</div>
                <div className="text-xs text-muted-foreground">
                  Pages Processed
                </div>
              </div>
              <div className="text-center">
                <div className="text-lg font-bold">
                  {Math.round(averageConfidence * 100)}%
                </div>
                <div className="text-xs text-muted-foreground">
                  Avg Confidence
                </div>
              </div>
              <div className="text-center">
                <div className="text-lg font-bold">
                  {Math.round(totalProcessingTime / 1000)}s
                </div>
                <div className="text-xs text-muted-foreground">Total Time</div>
              </div>
            </div>

            {/* Export Options */}
            <div className="flex gap-2">
              <Button
                size="sm"
                variant="outline"
                onClick={() => exportOCRResults("txt")}
              >
                <FileText className="h-4 w-4 mr-2" />
                Export TXT
              </Button>
              <Button
                size="sm"
                variant="outline"
                onClick={() => exportOCRResults("json")}
              >
                <Download className="h-4 w-4 mr-2" />
                Export JSON
              </Button>
              <Button
                size="sm"
                variant="outline"
                onClick={() => exportOCRResults("csv")}
              >
                <Download className="h-4 w-4 mr-2" />
                Export CSV
              </Button>
              <Button size="sm" variant="outline" onClick={copyAllText}>
                <Copy className="h-4 w-4 mr-2" />
                Copy All
              </Button>
            </div>
          </div>
        )}

        {/* Results List */}
        {ocrResults.length > 0 && (
          <ScrollArea className="flex-1">
            <div className="space-y-3">
              {ocrResults.map((result) => (
                <Card key={result.pageNumber} className="p-3">
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center gap-2">
                      <Badge variant="outline">Page {result.pageNumber}</Badge>
                      <Badge variant="secondary">{result.language}</Badge>
                      <Badge
                        variant={
                          result.confidence > 0.9
                            ? "default"
                            : result.confidence > 0.7
                            ? "secondary"
                            : "destructive"
                        }
                      >
                        {Math.round(result.confidence * 100)}% confidence
                      </Badge>
                    </div>
                    <div className="text-xs text-muted-foreground">
                      {result.processingTime}ms
                    </div>
                  </div>
                  <div className="text-sm bg-muted/50 rounded p-2 max-h-32 overflow-y-auto">
                    {result.text}
                  </div>
                  <div className="flex gap-2 mt-2">
                    <Button
                      size="sm"
                      variant="ghost"
                      onClick={() => navigator.clipboard.writeText(result.text)}
                    >
                      <Copy className="h-3 w-3 mr-1" />
                      Copy
                    </Button>
                    <Button size="sm" variant="ghost">
                      <Search className="h-3 w-3 mr-1" />
                      Search
                    </Button>
                  </div>
                </Card>
              ))}
            </div>
          </ScrollArea>
        )}

        {/* Empty State */}
        {ocrResults.length === 0 && !isProcessing && (
          <div className="flex flex-col items-center justify-center py-8 text-center">
            <ImageIcon className="h-12 w-12 text-muted-foreground mb-4" />
            <h3 className="font-medium text-lg mb-2">No OCR Results</h3>
            <p className="text-sm text-muted-foreground mb-4">
              Configure settings and click &quot;Start OCR&quot; to extract text from PDF
              pages
            </p>
            <div className="text-xs text-muted-foreground bg-muted/50 rounded-lg p-3 max-w-sm">
              <p className="font-medium mb-1">OCR Features:</p>
              <p>• Multi-language support</p>
              <p>• High accuracy text recognition</p>
              <p>• Word-level confidence scores</p>
              <p>• Multiple export formats</p>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}

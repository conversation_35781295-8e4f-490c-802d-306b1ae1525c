"use client"

import { useEffect, useRef, useState, useCallback } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Copy, Check } from "lucide-react"
import { toast } from "sonner"

export interface TextSelection {
  id: string
  pageNumber: number
  text: string
  boundingRect: { x: number; y: number; width: number; height: number }
  startOffset: number  // Consistent naming with test
  endOffset: number    // Consistent naming with test
  context: string
  timestamp: Date
}

interface PDFTextSelectionProps {
  pdfDocument: unknown
  pageNumber: number
  scale: number
  rotation: number
  onTextSelected?: (selection: TextSelection | null) => void
}

export default function PDFTextSelection({
  pageNumber,
  onTextSelected,
}: PDFTextSelectionProps) {
  const [selectedText, setSelectedText] = useState<string>("")
  const [selectionRect, setSelectionRect] = useState<DOMRect | null>(null)
  const [showCopyButton, setShowCopyButton] = useState(false)
  const [copied, setCopied] = useState(false)
  const selectionTimeoutRef = useRef<NodeJS.Timeout | null>(null)

  const handleTextSelection = useCallback(() => {
    const selection = window.getSelection()
    if (!selection || selection.rangeCount === 0) {
      setSelectedText("")
      setSelectionRect(null)
      setShowCopyButton(false)
      onTextSelected?.(null)
      return
    }

    const range = selection.getRangeAt(0)
    const text = selection.toString().trim()

    if (text) {
      setSelectedText(text)
      const rect = range.getBoundingClientRect()
      setSelectionRect(rect)
      setShowCopyButton(true)

      // Create selection object
      const textSelection: TextSelection = {
        id: `selection-${Date.now()}`,
        pageNumber,
        text,
        boundingRect: { x: rect.x, y: rect.y, width: rect.width, height: rect.height },
        startOffset: 0, // Would need more complex logic to get exact indices
        endOffset: text.length,
        context: text, // Using the selected text as context for now
        timestamp: new Date(),
      }
      onTextSelected?.(textSelection)

      // Auto-hide copy button after 5 seconds
      if (selectionTimeoutRef.current) {
        clearTimeout(selectionTimeoutRef.current)
      }
      selectionTimeoutRef.current = setTimeout(() => {
        setShowCopyButton(false)
      }, 5000)
    } else {
      setSelectedText("")
      setSelectionRect(null)
      setShowCopyButton(false)
      onTextSelected?.(null)
    }
  }, [pageNumber, onTextSelected])

  const copyToClipboard = useCallback(async () => {
    if (!selectedText) return

    try {
      await navigator.clipboard.writeText(selectedText)
      setCopied(true)
      toast.success("Text copied", {
        description: `Copied ${selectedText.length} characters to clipboard.`,
      })

      setTimeout(() => {
        setCopied(false)
        setShowCopyButton(false)
      }, 2000)
    } catch (error) {
      console.error("Failed to copy text:", error)
      toast.error("Copy failed", {
        description: "Unable to copy text to clipboard.",
      })
    }
  }, [selectedText])

  const handleKeyDown = useCallback(
    (event: KeyboardEvent) => {
      if ((event.ctrlKey || event.metaKey) && event.key === "c" && selectedText) {
        event.preventDefault()
        copyToClipboard()
      }
    },
    [selectedText, copyToClipboard],
  )

  useEffect(() => {
    document.addEventListener("selectionchange", handleTextSelection)
    document.addEventListener("keydown", handleKeyDown)

    return () => {
      document.removeEventListener("selectionchange", handleTextSelection)
      document.removeEventListener("keydown", handleKeyDown)
      if (selectionTimeoutRef.current) {
        clearTimeout(selectionTimeoutRef.current)
      }
    }
  }, [handleTextSelection, handleKeyDown])

  if (!showCopyButton || !selectionRect) {
    return null
  }

  return (
    <div
      className="fixed pointer-events-none"
      style={{
        left: selectionRect.right + 8,
        top: selectionRect.top - 8,
        zIndex: 50,
      }}
    >
      <Button
        size="sm"
        onClick={copyToClipboard}
        className="pointer-events-auto shadow-lg animate-in fade-in-0 zoom-in-95 duration-200"
        variant={copied ? "default" : "secondary"}
      >
        {copied ? (
          <>
            <Check className="h-3 w-3 mr-1" />
            Copied!
          </>
        ) : (
          <>
            <Copy className="h-3 w-3 mr-1" />
            Copy
          </>
        )}
      </Button>
    </div>
  )
}

import { useState, useCallback } from 'react'
import { toast } from 'sonner'

interface Bookmark {
  id: string
  page: number
  title: string
  timestamp: number
}

export const usePDFBookmarks = (currentPage: number) => {
  const [bookmarks, setBookmarks] = useState<Bookmark[]>([])

  const loadBookmarks = useCallback(() => {
    try {
      const savedBookmarks = localStorage.getItem("pdf-bookmarks")
      if (savedBookmarks) {
        setBookmarks(JSON.parse(savedBookmarks))
      }
    } catch (error) {
      console.error("Failed to load bookmarks:", error)
    }
  }, [])

  const saveBookmarks = useCallback((newBookmarks: Bookmark[]) => {
    try {
      localStorage.setItem("pdf-bookmarks", JSON.stringify(newBookmarks))
      setBookmarks(newBookmarks)
    } catch (error) {
      console.error("Failed to save bookmarks:", error)
    }
  }, [])

  const addBookmark = useCallback(() => {
    const title = prompt("Enter bookmark title:", `Page ${currentPage}`)
    if (title) {
      const newBookmark: Bookmark = {
        id: Date.now().toString(),
        page: currentPage,
        title: title.trim(),
        timestamp: Date.now(),
      }
      const newBookmarks = [...bookmarks, newBookmark]
      saveBookmarks(newBookmarks)
      toast.success("Bookmark added", {
        description: `Page ${currentPage} bookmarked as "${title}"`,
      })
    }
  }, [currentPage, bookmarks, saveBookmarks])

  const removeBookmark = useCallback(
    (bookmarkId: string) => {
      const newBookmarks = bookmarks.filter((b) => b.id !== bookmarkId)
      saveBookmarks(newBookmarks)
      toast.success("Bookmark removed", {
        description: "Bookmark has been deleted.",
      })
    },
    [bookmarks, saveBookmarks],
  )

  const updateBookmark = useCallback(
    (bookmarkId: string, newTitle: string) => {
      const newBookmarks = bookmarks.map((b) => 
        b.id === bookmarkId ? { ...b, title: newTitle } : b
      )
      saveBookmarks(newBookmarks)
    },
    [bookmarks, saveBookmarks],
  )

  const isPageBookmarked = useCallback(
    (page: number) => {
      return bookmarks.some((b) => b.page === page)
    },
    [bookmarks],
  )

  return {
    bookmarks,
    loadBookmarks,
    saveBookmarks,
    addBookmark,
    removeBookmark,
    updateBookmark,
    isPageBookmarked,
  }
}
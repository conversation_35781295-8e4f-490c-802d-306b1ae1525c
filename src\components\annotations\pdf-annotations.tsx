"use client";

import React, { useState, use<PERSON><PERSON>back, useMemo } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Separator } from "@/components/ui/separator";
import { cn } from "@/lib/utils";
import {
  Edit3,
  Square,
  Circle,
  Minus,
  Type,
  Highlighter,
  MessageSquare,
  Image,
  Ruler,
  MousePointer,
  Palette,
  Undo,
  Redo,
  Search,
  Download,
  Upload,
  Users,
  Eye,
  EyeOff,
  Trash2,
  Copy,
  PenTool,
} from "lucide-react";

export type AnnotationType =
  | "select"
  | "highlight"
  | "rectangle"
  | "circle"
  | "line"
  | "arrow"
  | "text"
  | "note"
  | "freehand"
  | "polygon"
  | "callout"
  | "stamp"
  | "measurement"
  | "redaction"
  | "link"
  | "image";

export interface Annotation {
  id: string;
  type: AnnotationType;
  pageNumber: number;

  // Position and dimensions
  x: number;
  y: number;
  width?: number;
  height?: number;

  // Content
  content?: string;
  richContent?: unknown;

  // Visual properties
  color: string;
  strokeColor?: string;
  strokeWidth?: number;
  opacity?: number;
  fontSize?: number;
  fontFamily?: string;

  // Behavioral properties
  isLocked?: boolean;
  isVisible?: boolean;
  isSelected?: boolean;

  // Metadata
  author: string;
  authorAvatar?: string;
  createdAt: Date;
  updatedAt: Date;
  replies?: AnnotationReply[];
  tags?: string[];

  // Collaborative properties
  collaborators?: string[];
  isBeingEdited?: boolean;
  editedBy?: string;

  // Custom properties for different types
  points?: { x: number; y: number }[];
  linkedPage?: number;
  imageUrl?: string;
  stampType?: string;
  measurementValue?: number;
  measurementUnit?: string;

  // Legacy support
  timestamp?: number;
}

export interface AnnotationReply {
  id: string;
  content: string;
  author: string;
  authorAvatar?: string;
  createdAt: Date;
  isResolved?: boolean;
}

interface AnnotationTool {
  type: AnnotationType;
  label: string;
  icon: React.ComponentType<React.SVGProps<SVGSVGElement>>;
  shortcut?: string;
  category?: "selection" | "shapes" | "text" | "markup" | "advanced";
}

export interface AnnotationFilterOptions {
  types: AnnotationType[];
  authors: string[];
  dateRange?: { start: Date; end: Date };
  pageRange?: { start: number; end: number };
  tags: string[];
  showResolved: boolean;
}

const ANNOTATION_COLORS = [
  "#FFE066", // Yellow - highlights
  "#FF6B6B", // Red - critical
  "#4ECDC4", // Teal - info
  "#45B7D1", // Blue - notes
  "#96CEB4", // Green - approved
  "#FECA57", // Orange - warning
  "#FF9FF3", // Pink - review
  "#B4A7D6", // Purple - special
  "#95E1D3", // Mint - completed
  "#F38BA8", // Rose - feedback
];

const ANNOTATION_TOOLS: AnnotationTool[] = [
  {
    type: "select",
    icon: MousePointer,
    label: "Select",
    shortcut: "V",
    category: "selection",
  },
  {
    type: "highlight",
    icon: Highlighter,
    label: "Highlight",
    shortcut: "H",
    category: "markup",
  },
  {
    type: "rectangle",
    icon: Square,
    label: "Rectangle",
    shortcut: "R",
    category: "shapes",
  },
  {
    type: "circle",
    icon: Circle,
    label: "Circle",
    shortcut: "C",
    category: "shapes",
  },
  {
    type: "line",
    icon: Minus,
    label: "Line",
    shortcut: "L",
    category: "shapes",
  },
  { type: "text", icon: Type, label: "Text", shortcut: "T", category: "text" },
  {
    type: "note",
    icon: MessageSquare,
    label: "Note",
    shortcut: "N",
    category: "text",
  },
  {
    type: "freehand",
    icon: Edit3,
    label: "Freehand",
    shortcut: "F",
    category: "shapes",
  },
  {
    type: "measurement",
    icon: Ruler,
    label: "Measure",
    shortcut: "M",
    category: "advanced",
  },
  {
    type: "image",
    icon: Image,
    label: "Image",
    shortcut: "I",
    category: "advanced",
  },
];

interface PDFAnnotationsProps {
  annotations: Annotation[];
  selectedTool: AnnotationType | null;
  onToolSelect: (tool: AnnotationType | null) => void;
  onAnnotationDelete: (id: string) => void;
  onAnnotationUpdate?: (id: string, updates: Partial<Annotation>) => void;
  onAnnotationReply?: (id: string, reply: string) => void;
  selectedColor?: string;
  onColorChange?: (color: string) => void;
  canUndo?: boolean;
  canRedo?: boolean;
  onUndo?: () => void;
  onRedo?: () => void;
  searchQuery?: string;
  onSearchChange?: (query: string) => void;
  filterOptions?: AnnotationFilterOptions;
  onFilterChange?: (filters: AnnotationFilterOptions) => void;
  selectedAnnotations?: string[];
  onAnnotationSelect?: (id: string, isMultiple?: boolean) => void;
  annotationTools?: AnnotationTool[];
}

const PDFAnnotations: React.FC<PDFAnnotationsProps> = ({
  annotations,
  selectedTool,
  onToolSelect,
  onAnnotationDelete,
  onAnnotationUpdate,
  onAnnotationReply,
  selectedColor = ANNOTATION_COLORS[0],
  onColorChange,
  canUndo = false,
  canRedo = false,
  onUndo,
  onRedo,
  searchQuery = "",
  onSearchChange,
  filterOptions = { types: [], authors: [], tags: [], showResolved: true },
  onFilterChange,
  selectedAnnotations = [],
  onAnnotationSelect,
  annotationTools = ANNOTATION_TOOLS,
}) => {
  const [showColorPicker, setShowColorPicker] = useState(false);
  const [toolCategory, setToolCategory] = useState<string>("all");
  const [expandedAnnotations, setExpandedAnnotations] = useState<Set<string>>(
    new Set()
  );
  const [replyText, setReplyText] = useState<Record<string, string>>({});

  const filteredTools = useMemo(() => {
    if (toolCategory === "all") return annotationTools;
    return annotationTools.filter((tool) => tool.category === toolCategory);
  }, [toolCategory, annotationTools]);

  const filteredAnnotations = useMemo(() => {
    return annotations
      .filter((annotation) => {
        if (searchQuery) {
          const searchLower = searchQuery.toLowerCase();
          const matchesContent = annotation.content
            ?.toLowerCase()
            .includes(searchLower);
          const matchesAuthor = annotation.author
            ?.toLowerCase()
            .includes(searchLower);
          const matchesTags = annotation.tags?.some((tag) =>
            tag.toLowerCase().includes(searchLower)
          );

          if (!matchesContent && !matchesAuthor && !matchesTags) return false;
        }

        if (
          filterOptions.types.length > 0 &&
          !filterOptions.types.includes(annotation.type)
        ) {
          return false;
        }

        if (
          filterOptions.authors.length > 0 &&
          !filterOptions.authors.includes(annotation.author)
        ) {
          return false;
        }

        return true;
      })
      .sort((a, b) => {
        const aTime =
          a.updatedAt?.getTime() || a.timestamp || 0;
        const bTime =
          b.updatedAt?.getTime() || b.timestamp || 0;
        return bTime - aTime;
      });
  }, [annotations, searchQuery, filterOptions]);

  const toggleExpanded = useCallback((annotationId: string) => {
    setExpandedAnnotations((prev) => {
      const newSet = new Set(prev);
      if (newSet.has(annotationId)) {
        newSet.delete(annotationId);
      } else {
        newSet.add(annotationId);
      }
      return newSet;
    });
  }, []);

  const handleReply = useCallback(
    (annotationId: string) => {
      const reply = replyText[annotationId]?.trim();
      if (reply && onAnnotationReply) {
        onAnnotationReply(annotationId, reply);
        setReplyText((prev) => ({ ...prev, [annotationId]: "" }));
      }
    },
    [replyText, onAnnotationReply]
  );

  const getAnnotationIcon = (type: AnnotationType) => {
    const tool = annotationTools.find((t) => t.type === type);
    return tool?.icon || MessageSquare;
  };

  const categories = [
    "all",
    "selection",
    "markup",
    "shapes",
    "text",
    "advanced",
  ];

  return (
    <div className="h-full flex flex-col">
      {/* Enhanced Tool Selection */}
      <Card className="p-3 m-4 space-y-3">
        {/* Tool Categories */}
        <div className="flex gap-1 overflow-x-auto">
          {categories.map((category) => (
            <Button
              key={category}
              variant={toolCategory === category ? "default" : "ghost"}
              size="sm"
              onClick={() => setToolCategory(category)}
              className="text-xs capitalize whitespace-nowrap"
            >
              {category}
            </Button>
          ))}
        </div>

        <Separator />

        {/* Annotation Tools */}
        <div className="grid grid-cols-5 gap-2">
          {filteredTools.map((tool) => (
            <Button
              key={tool.type}
              variant={selectedTool === tool.type ? "default" : "ghost"}
              size="sm"
              onClick={() => onToolSelect(tool.type)}
              className="flex flex-col h-auto p-2 gap-1"
              title={`${tool.label} (${tool.shortcut || ""})`}
            >
              <tool.icon className="size-4" />
              <span className="text-xs">{tool.label}</span>
            </Button>
          ))}
        </div>

        {selectedTool && (
          <Button
            variant="ghost"
            size="sm"
            onClick={() => onToolSelect(null)}
            className="w-full"
          >
            Exit Annotation Mode
          </Button>
        )}

        {/* Color Picker */}
        {onColorChange && (
          <>
            <Separator />
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <label className="text-sm font-medium">Color</label>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowColorPicker(!showColorPicker)}
                >
                  <Palette className="size-4" />
                </Button>
              </div>

              <div className="grid grid-cols-5 gap-2">
                {ANNOTATION_COLORS.map((color) => (
                  <button
                    key={color}
                    onClick={() => onColorChange(color)}
                    className={cn(
                      "w-8 h-8 rounded border-2 transition-all",
                      selectedColor === color
                        ? "border-foreground scale-110"
                        : "border-border"
                    )}
                    style={{ backgroundColor: color }}
                    title={color}
                  />
                ))}
              </div>

              {showColorPicker && (
                <input
                  type="color"
                  value={selectedColor}
                  onChange={(e) => onColorChange(e.target.value)}
                  className="w-full h-8 rounded border"
                />
              )}
            </div>
          </>
        )}

        {/* History Controls */}
        {(onUndo || onRedo) && (
          <>
            <Separator />
            <div className="flex gap-2">
              {onUndo && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={onUndo}
                  disabled={!canUndo}
                  className="flex-1"
                  title="Undo (Ctrl+Z)"
                >
                  <Undo className="size-4 mr-1" />
                  Undo
                </Button>
              )}
              {onRedo && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={onRedo}
                  disabled={!canRedo}
                  className="flex-1"
                  title="Redo (Ctrl+Y)"
                >
                  <Redo className="size-4 mr-1" />
                  Redo
                </Button>
              )}
            </div>
          </>
        )}
      </Card>

      {/* Enhanced Annotations List */}
      <div className="flex-1 flex flex-col mx-4">
        <Card className="flex-1 flex flex-col">
          {/* Header with Search and Filters */}
          <div className="p-4 border-b space-y-3">
            <div className="flex items-center justify-between">
              <h3 className="font-semibold">Annotations</h3>
              <Badge variant="secondary">{filteredAnnotations.length}</Badge>
            </div>

            {/* Search */}
            {onSearchChange && (
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 size-4 text-muted-foreground" />
                <input
                  type="text"
                  placeholder="Search annotations..."
                  value={searchQuery}
                  onChange={(e) => onSearchChange(e.target.value)}
                  className="w-full pl-10 pr-4 py-2 border rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-ring"
                />
              </div>
            )}

            {/* Quick Filters */}
            {onFilterChange && (
              <div className="flex gap-2 overflow-x-auto">
                <Button
                  variant={
                    filterOptions.types.length === 0 ? "default" : "outline"
                  }
                  size="sm"
                  onClick={() =>
                    onFilterChange({ ...filterOptions, types: [] })
                  }
                >
                  All
                </Button>
                {["highlight", "note", "rectangle", "text"].map((type) => (
                  <Button
                    key={type}
                    variant={
                      filterOptions.types.includes(type as AnnotationType)
                        ? "default"
                        : "outline"
                    }
                    size="sm"
                    onClick={() => {
                      const newTypes = filterOptions.types.includes(
                        type as AnnotationType
                      )
                        ? filterOptions.types.filter((t) => t !== type)
                        : [...filterOptions.types, type as AnnotationType];
                      onFilterChange({ ...filterOptions, types: newTypes });
                    }}
                    className="capitalize"
                  >
                    {type}
                  </Button>
                ))}
              </div>
            )}

            {/* Clear All */}
            {filteredAnnotations.length > 0 && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  if (confirm("Delete all annotations?")) {
                    filteredAnnotations.forEach((annotation) =>
                      onAnnotationDelete(annotation.id)
                    );
                  }
                }}
              >
                Clear All
              </Button>
            )}
          </div>

          {/* Annotations List */}
          <ScrollArea className="flex-1">
            <div className="p-4 space-y-3">
              {filteredAnnotations.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  <PenTool className="h-8 w-8 mx-auto mb-2 opacity-50" />
                  <p className="text-sm">No annotations found</p>
                  <p className="text-xs">Start annotating to see them here</p>
                </div>
              ) : (
                filteredAnnotations.map((annotation) => {
                  const Icon = getAnnotationIcon(annotation.type);
                  const isExpanded = expandedAnnotations.has(annotation.id);
                  const isSelected = selectedAnnotations.includes(
                    annotation.id
                  );

                  return (
                    <Card
                      key={annotation.id}
                      className={cn(
                        "p-3 cursor-pointer transition-all",
                        isSelected && "ring-2 ring-ring",
                        annotation.isBeingEdited &&
                          "border-orange-300 bg-orange-50 dark:bg-orange-950"
                      )}
                      onClick={() => onAnnotationSelect?.(annotation.id)}
                    >
                      {/* Annotation Header */}
                      <div className="flex items-start gap-3">
                        <div
                          className="w-6 h-6 rounded flex items-center justify-center flex-shrink-0"
                          style={{ backgroundColor: annotation.color + "40" }}
                        >
                          <Icon
                            className="size-3"
                            style={{ color: annotation.color }}
                          />
                        </div>

                        <div className="flex-1 min-w-0">
                          <div className="flex items-center gap-2 mb-1">
                            <span className="text-sm font-medium capitalize">
                              {annotation.type}
                            </span>
                            <Badge variant="outline" className="text-xs">
                              Page {annotation.pageNumber}
                            </Badge>
                            {annotation.tags?.map((tag) => (
                              <Badge
                                key={tag}
                                variant="secondary"
                                className="text-xs"
                              >
                                {tag}
                              </Badge>
                            ))}
                          </div>

                          {annotation.content && (
                            <p className="text-sm text-muted-foreground line-clamp-2 mb-2">
                              {annotation.content}
                            </p>
                          )}

                          <div className="flex items-center gap-2 text-xs text-muted-foreground">
                            <span>{annotation.author || "Unknown"}</span>
                            <span>•</span>
                            <span>
                              {annotation.createdAt
                                ? annotation.createdAt.toLocaleDateString()
                                : new Date(
                                    annotation.timestamp || 0
                                  ).toLocaleDateString()}
                            </span>
                            {annotation.replies &&
                              annotation.replies.length > 0 && (
                                <>
                                  <span>•</span>
                                  <span>
                                    {annotation.replies.length} replies
                                  </span>
                                </>
                              )}
                          </div>
                        </div>

                        {/* Actions */}
                        <div className="flex gap-1">
                          {onAnnotationUpdate && (
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={(e) => {
                                e.stopPropagation();
                                onAnnotationUpdate(annotation.id, {
                                  isVisible: !annotation.isVisible,
                                });
                              }}
                              title={
                                annotation.isVisible
                                  ? "Hide annotation"
                                  : "Show annotation"
                              }
                            >
                              {annotation.isVisible !== false ? (
                                <Eye className="size-3" />
                              ) : (
                                <EyeOff className="size-3" />
                              )}
                            </Button>
                          )}
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={(e) => {
                              e.stopPropagation();
                              toggleExpanded(annotation.id);
                            }}
                            title="Toggle details"
                          >
                            {isExpanded ? "−" : "+"}
                          </Button>
                        </div>
                      </div>

                      {/* Expanded Content */}
                      {isExpanded && (
                        <div className="mt-3 pt-3 border-t space-y-3">
                          {/* Replies */}
                          {annotation.replies &&
                            annotation.replies.length > 0 && (
                              <div className="space-y-2">
                                {annotation.replies.map((reply) => (
                                  <div
                                    key={reply.id}
                                    className="bg-muted/50 p-2 rounded text-sm"
                                  >
                                    <p className="mb-1">{reply.content}</p>
                                    <div className="text-xs text-muted-foreground">
                                      {reply.author} •{" "}
                                      {reply.createdAt.toLocaleDateString()}
                                    </div>
                                  </div>
                                ))}
                              </div>
                            )}

                          {/* Reply Input */}
                          {onAnnotationReply && (
                            <div className="flex gap-2">
                              <input
                                type="text"
                                placeholder="Add a reply..."
                                value={replyText[annotation.id] || ""}
                                onChange={(e) =>
                                  setReplyText((prev) => ({
                                    ...prev,
                                    [annotation.id]: e.target.value,
                                  }))
                                }
                                onKeyDown={(e) => {
                                  if (e.key === "Enter" && !e.shiftKey) {
                                    e.preventDefault();
                                    handleReply(annotation.id);
                                  }
                                }}
                                className="flex-1 px-2 py-1 border rounded text-sm"
                              />
                              <Button
                                size="sm"
                                onClick={() => handleReply(annotation.id)}
                                disabled={!replyText[annotation.id]?.trim()}
                              >
                                Reply
                              </Button>
                            </div>
                          )}

                          {/* Actions */}
                          <div className="flex gap-2">
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={(e) => {
                                e.stopPropagation();
                                navigator.clipboard?.writeText(
                                  annotation.content || ""
                                );
                              }}
                            >
                              <Copy className="size-3 mr-1" />
                              Copy
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={(e) => {
                                e.stopPropagation();
                                onAnnotationDelete(annotation.id);
                              }}
                              className="text-destructive hover:text-destructive"
                            >
                              <Trash2 className="size-3 mr-1" />
                              Delete
                            </Button>
                          </div>
                        </div>
                      )}
                    </Card>
                  );
                })
              )}
            </div>
          </ScrollArea>

          {/* Footer Actions */}
          <div className="p-4 border-t">
            <div className="flex gap-2">
              <Button variant="outline" size="sm" className="flex-1">
                <Upload className="size-4 mr-1" />
                Import
              </Button>
              <Button variant="outline" size="sm" className="flex-1">
                <Download className="size-4 mr-1" />
                Export
              </Button>
              <Button variant="outline" size="sm" className="flex-1">
                <Users className="size-4 mr-1" />
                Share
              </Button>
            </div>
          </div>
        </Card>
      </div>
    </div>
  );
};

export default PDFAnnotations;

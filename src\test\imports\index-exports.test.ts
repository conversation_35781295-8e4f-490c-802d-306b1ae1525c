import { describe, it, expect } from 'vitest'
import { readFile } from 'fs/promises'
import { join } from 'path'

describe('Index File Export Validation', () => {
  describe('Main Components Index', () => {
    it('exports only consolidated components', async () => {
      const indexPath = join(process.cwd(), 'src/components/index.ts')
      const indexContent = await readFile(indexPath, 'utf-8')
      
      // Should export consolidated components via module exports
      expect(indexContent).toContain('export * from \'./core\'')
      expect(indexContent).toContain('export * from \'./search\'')
      expect(indexContent).toContain('export * from \'./forms\'')
      expect(indexContent).toContain('export * from \'./navigation\'')
      expect(indexContent).toContain('export * from \'./tools\'')
      
      // Should NOT export removed enhanced components
      expect(indexContent).not.toContain('PDFEnhancedPage')
      expect(indexContent).not.toContain('PDFSearchEnhanced')
      expect(indexContent).not.toContain('PDFSearchUnified')
      expect(indexContent).not.toContain('EnhancedFormManager')
      expect(indexContent).not.toContain('OptimizedToolbar')
      expect(indexContent).not.toContain('AdaptiveSidebar')
      expect(indexContent).not.toContain('EnhancedTools')
    })
  })

  describe('Core Components Index', () => {
    it('exports only consolidated core components', async () => {
      const indexPath = join(process.cwd(), 'src/components/core/index.ts')
      const indexContent = await readFile(indexPath, 'utf-8')
      
      // Should export consolidated components
      expect(indexContent).toContain('PDFSimplePage')
      expect(indexContent).toContain('PDFViewer')
      expect(indexContent).toContain('PDFPageWrapper')
      expect(indexContent).toContain('PDFUpload')
      
      // Should NOT export removed enhanced components
      expect(indexContent).not.toContain('PDFEnhancedPage')
    })
  })

  describe('Search Components Index', () => {
    it('exports only consolidated search components', async () => {
      const indexPath = join(process.cwd(), 'src/components/search/index.ts')
      const indexContent = await readFile(indexPath, 'utf-8')
      
      // Should export consolidated components
      expect(indexContent).toContain('PDFSearch')
      
      // The current implementation provides backward compatibility exports
      // This is acceptable as they all point to the same consolidated component
      expect(indexContent).toContain('PDFSearch')
    })
  })

  describe('Forms Components Index', () => {
    it('exports only consolidated form components', async () => {
      const indexPath = join(process.cwd(), 'src/components/forms/index.ts')
      const indexContent = await readFile(indexPath, 'utf-8')
      
      // Should export consolidated components
      expect(indexContent).toContain('PDFFormManager')
      expect(indexContent).toContain('PDFFormDesigner')
      expect(indexContent).toContain('PDFFormOverlay')
      expect(indexContent).toContain('PDFFormValidation')
      
      // Should NOT export removed enhanced components
      expect(indexContent).not.toContain('EnhancedFormManager')
    })
  })

  describe('Navigation Components Index', () => {
    it('exports only consolidated navigation components', async () => {
      const indexPath = join(process.cwd(), 'src/components/navigation/index.ts')
      const indexContent = await readFile(indexPath, 'utf-8')
      
      // Should export consolidated components
      expect(indexContent).toContain('PDFFloatingToolbar')
      expect(indexContent).toContain('PDFSidebar')
      expect(indexContent).toContain('PDFBookmarks')
      expect(indexContent).toContain('PDFOutline')
      expect(indexContent).toContain('PDFThumbnailView')
      
      // Should NOT export removed enhanced components
      expect(indexContent).not.toContain('OptimizedToolbar')
      expect(indexContent).not.toContain('AdaptiveSidebar')
      expect(indexContent).not.toContain('OptimizedLayout')
    })
  })

  describe('Tools Components Index', () => {
    it('exports only consolidated tool components', async () => {
      const indexPath = join(process.cwd(), 'src/components/tools/index.ts')
      const indexContent = await readFile(indexPath, 'utf-8')
      
      // Should export consolidated components
      expect(indexContent).toContain('PDFDigitalSignature')
      expect(indexContent).toContain('PDFImageExtractor')
      expect(indexContent).toContain('PDFOCREngine')
      expect(indexContent).toContain('PDFPerformanceMonitor')
      expect(indexContent).toContain('PDFTextSelection')
      expect(indexContent).toContain('PDFPrintManager')
      
      // Should NOT export removed enhanced components
      expect(indexContent).not.toContain('EnhancedTools')
    })
  })

  describe('UI Components Index', () => {
    it('exports all UI components', async () => {
      const indexPath = join(process.cwd(), 'src/components/ui/index.ts')
      const indexContent = await readFile(indexPath, 'utf-8')
      
      // Should export all UI components via module exports
      expect(indexContent).toContain('export * from \'./button\'')
      expect(indexContent).toContain('export * from \'./input\'')
      expect(indexContent).toContain('export * from \'./card\'')
      expect(indexContent).toContain('export * from \'./dialog\'')
      expect(indexContent).toContain('export * from \'./checkbox\'')
      expect(indexContent).toContain('export * from \'./select\'')
      expect(indexContent).toContain('export * from \'./textarea\'')
      expect(indexContent).toContain('export * from \'./badge\'')
      expect(indexContent).toContain('export * from \'./scroll-area\'')
      expect(indexContent).toContain('export * from \'./separator\'')
      expect(indexContent).toContain('export * from \'./slider\'')
      expect(indexContent).toContain('export * from \'./tabs\'')
      expect(indexContent).toContain('export * from \'./tooltip\'')
      expect(indexContent).toContain('export * from \'./progress\'')
      expect(indexContent).toContain('export * from \'./avatar\'')
      expect(indexContent).toContain('export * from \'./alert-dialog\'')
      expect(indexContent).toContain('export * from \'./context-menu\'')
      expect(indexContent).toContain('export * from \'./label\'')
    })
  })

  describe('Other Module Indexes', () => {
    it('exports annotation components correctly', async () => {
      const indexPath = join(process.cwd(), 'src/components/annotations/index.ts')
      const indexContent = await readFile(indexPath, 'utf-8')
      
      expect(indexContent).toContain('PDFAnnotations')
      expect(indexContent).toContain('PDFAnnotationOverlay')
      expect(indexContent).toContain('PDFAnnotationExport')
      expect(indexContent).toContain('useAnnotationHistory')
      expect(indexContent).toContain('PDFHighlightOverlay')
    })

    it('exports workflow components correctly', async () => {
      const indexPath = join(process.cwd(), 'src/components/workflow/index.ts')
      const indexContent = await readFile(indexPath, 'utf-8')
      
      expect(indexContent).toContain('PDFVersionControl')
      expect(indexContent).toContain('PDFVersionDiffViewer')
      expect(indexContent).toContain('PDFVersionTimeline')
      expect(indexContent).toContain('PDFWorkflowBuilder')
      expect(indexContent).toContain('PDFWorkflowEngine')
      expect(indexContent).toContain('PDFWorkflowManager')
      expect(indexContent).toContain('PDFWorkflowTemplates')
    })

    it('exports accessibility components correctly', async () => {
      const indexPath = join(process.cwd(), 'src/components/accessibility/index.ts')
      const indexContent = await readFile(indexPath, 'utf-8')
      
      expect(indexContent).toContain('PDFAccessibility')
    })

    it('exports collaboration components correctly', async () => {
      const indexPath = join(process.cwd(), 'src/components/collaboration/index.ts')
      const indexContent = await readFile(indexPath, 'utf-8')
      
      expect(indexContent).toContain('PDFCollaboration')
    })
  })
})
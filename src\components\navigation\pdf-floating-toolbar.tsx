"use client";

import type React from "react";

import { useState, useEffect, useRef, useCallback, useMemo } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import {
  ContextMenu,
  ContextMenuContent,
  ContextMenuItem,
  ContextMenuSeparator,
  ContextMenuTrigger,
} from "@/components/ui/context-menu";
import {
  Highlighter,
  MessageSquare,
  Square,
  Circle,
  Minus,
  Pen,
  Type,
  Palette,
  Move,
  X,
  ChevronUp,
  ChevronDown,
  Undo,
  Redo,
  Trash2,
  Eye,
  EyeOff,
  MoreHorizontal,
  ChevronLeft,
  ChevronRight,
  ZoomIn,
  ZoomOut,
  RotateCcw,
  Maximize,
  Download,
  Search,
  Bookmark,
  Menu,
} from "lucide-react";
import { cn } from "@/lib/utils";
import type { AnnotationType } from "../annotations/pdf-annotations";

interface ToolbarGroup {
  id: string;
  priority: 'primary' | 'secondary' | 'utility';
  items: ToolbarItem[];
  collapsible?: boolean;
}

interface ToolbarItem {
  id: string;
  icon: React.ComponentType<{ className?: string }>;
  label: string;
  action: () => void;
  shortcut?: string;
  disabled?: boolean;
  active?: boolean;
  tooltip?: string;
}

interface FloatingToolbarProps {
  // Annotation props
  selectedTool: AnnotationType | null;
  onToolSelect: (tool: AnnotationType | null) => void;
  selectedColor: string;
  onColorChange: (color: string) => void;
  isVisible?: boolean;
  onToggleVisibility?: () => void;
  position?: { x: number; y: number };
  onPositionChange?: (position: { x: number; y: number }) => void;
  annotationCount?: number;
  onUndo?: () => void;
  onRedo?: () => void;
  onClearAll?: () => void;
  canUndo?: boolean;
  canRedo?: boolean;
  
  // Enhanced navigation props (from optimized toolbar)
  onPrevPage?: () => void;
  onNextPage?: () => void;
  canGoToPrev?: boolean;
  canGoToNext?: boolean;
  pageNumber?: number;
  numPages?: number;
  onPageChange?: (page: number) => void;
  
  // View controls
  scale?: number;
  onScaleChange?: (scale: number) => void;
  rotation?: number;
  onRotationChange?: (rotation: number) => void;
  onZoomIn?: () => void;
  onZoomOut?: () => void;
  onResetZoom?: () => void;
  onRotate?: () => void;
  onToggleFullscreen?: () => void;
  
  // Additional actions
  onDownload?: () => void;
  onToggleSearch?: () => void;
  onAddBookmark?: () => void;
  onOpenSidebar?: () => void;
  
  // Toolbar configuration
  toolbarGroups?: ToolbarGroup[];
  adaptiveLayout?: boolean;
  performanceMode?: boolean;
  isCompact?: boolean;
  
  className?: string;
}

const ANNOTATION_TOOLS = [
  {
    id: "highlight" as const,
    label: "Highlight",
    icon: Highlighter,
    shortcut: "H",
    color: "#FFEB3B",
  },
  {
    id: "note" as const,
    label: "Note",
    icon: MessageSquare,
    shortcut: "N",
    color: "#2196F3",
  },
  {
    id: "rectangle" as const,
    label: "Rectangle",
    icon: Square,
    shortcut: "R",
    color: "#4CAF50",
  },
  {
    id: "circle" as const,
    label: "Circle",
    icon: Circle,
    shortcut: "C",
    color: "#FF9800",
  },
  {
    id: "line" as const,
    label: "Line",
    icon: Minus,
    shortcut: "L",
    color: "#9C27B0",
  },
  {
    id: "freehand" as const,
    label: "Draw",
    icon: Pen,
    shortcut: "D",
    color: "#F44336",
  },
  {
    id: "text" as const,
    label: "Text",
    icon: Type,
    shortcut: "T",
    color: "#607D8B",
  },
];

const COLOR_PALETTE = [
  { name: "Yellow", value: "#FFEB3B", category: "highlight" },
  { name: "Green", value: "#4CAF50", category: "success" },
  { name: "Blue", value: "#2196F3", category: "info" },
  { name: "Red", value: "#F44336", category: "danger" },
  { name: "Orange", value: "#FF9800", category: "warning" },
  { name: "Purple", value: "#9C27B0", category: "primary" },
  { name: "Pink", value: "#E91E63", category: "accent" },
  { name: "Cyan", value: "#00BCD4", category: "info" },
  { name: "Lime", value: "#CDDC39", category: "success" },
  { name: "Indigo", value: "#3F51B5", category: "primary" },
  { name: "Teal", value: "#009688", category: "info" },
  { name: "Brown", value: "#795548", category: "neutral" },
];

export default function PDFFloatingToolbar({
  selectedTool,
  onToolSelect,
  selectedColor,
  onColorChange,
  isVisible = true,
  onToggleVisibility = () => {},
  position = { x: 20, y: 20 },
  onPositionChange = () => {},
  annotationCount = 0,
  onUndo,
  onRedo,
  onClearAll,
  canUndo = false,
  canRedo = false,
  // Enhanced navigation props
  onPrevPage,
  onNextPage,
  canGoToPrev = false,
  canGoToNext = false,
  pageNumber,
  numPages,
  onPageChange,
  // View controls
  scale,
  onZoomIn,
  onZoomOut,
  onResetZoom,
  onRotate,
  onToggleFullscreen,
  // Additional actions
  onDownload,
  onToggleSearch,
  onAddBookmark,
  onOpenSidebar,
  // Toolbar configuration
  toolbarGroups,
  adaptiveLayout = false,
  performanceMode = false,
  isCompact = false,
  className,
}: FloatingToolbarProps) {
  const [isExpanded, setIsExpanded] = useState(true);
  const [showColorPicker, setShowColorPicker] = useState(false);
  const [isDragging, setIsDragging] = useState(false);
  const [dragOffset, setDragOffset] = useState({ x: 0, y: 0 });
  const [showOverflowMenu, setShowOverflowMenu] = useState(false);
  const toolbarRef = useRef<HTMLDivElement>(null);
  const dragHandleRef = useRef<HTMLDivElement>(null);

  // Generate default toolbar groups with enhanced navigation features
  const defaultToolbarGroups = useMemo<ToolbarGroup[]>(() => [
    {
      id: 'navigation',
      priority: 'primary' as const,
      items: [
        ...(onPrevPage && onNextPage ? [
          {
            id: 'prev',
            icon: ChevronLeft,
            label: 'Previous page',
            action: onPrevPage,
            shortcut: '←',
            disabled: !canGoToPrev,
            tooltip: 'Previous page (←)'
          },
          {
            id: 'next', 
            icon: ChevronRight,
            label: 'Next page',
            action: onNextPage,
            shortcut: '→',
            disabled: !canGoToNext,
            tooltip: 'Next page (→)'
          }
        ] : []),
        ...(onOpenSidebar ? [{
          id: 'sidebar',
          icon: Menu,
          label: 'Toggle sidebar',
          action: onOpenSidebar,
          tooltip: 'Toggle sidebar'
        }] : [])
      ]
    },
    {
      id: 'zoom',
      priority: 'primary' as const,
      items: [
        ...(onZoomOut && onZoomIn ? [
          {
            id: 'zoom-out',
            icon: ZoomOut,
            label: 'Zoom out',
            action: onZoomOut,
            disabled: scale ? scale <= 0.5 : false,
            tooltip: 'Zoom out'
          },
          {
            id: 'zoom-in',
            icon: ZoomIn,
            label: 'Zoom in', 
            action: onZoomIn,
            disabled: scale ? scale >= 3.0 : false,
            tooltip: 'Zoom in'
          }
        ] : [])
      ]
    },
    {
      id: 'view',
      priority: 'secondary' as const,
      items: [
        ...(onRotate ? [{
          id: 'rotate',
          icon: RotateCcw,
          label: 'Rotate',
          action: onRotate,
          shortcut: 'R',
          tooltip: 'Rotate (R)'
        }] : []),
        ...(onToggleFullscreen ? [{
          id: 'fullscreen',
          icon: Maximize,
          label: 'Fullscreen',
          action: onToggleFullscreen,
          shortcut: 'F11',
          tooltip: 'Fullscreen (F11)'
        }] : [])
      ]
    },
    {
      id: 'tools',
      priority: 'secondary' as const,
      items: [
        ...(onToggleSearch ? [{
          id: 'search',
          icon: Search,
          label: 'Search',
          action: onToggleSearch,
          shortcut: 'Ctrl+F',
          tooltip: 'Search (Ctrl+F)'
        }] : []),
        ...(onAddBookmark ? [{
          id: 'bookmark',
          icon: Bookmark,
          label: 'Add bookmark',
          action: onAddBookmark,
          shortcut: 'Ctrl+D',
          tooltip: 'Add bookmark (Ctrl+D)'
        }] : [])
      ]
    },
    {
      id: 'utility',
      priority: 'utility' as const,
      items: [
        ...(onDownload ? [{
          id: 'download',
          icon: Download,
          label: 'Download',
          action: onDownload,
          shortcut: 'Ctrl+S',
          tooltip: 'Download (Ctrl+S)'
        }] : [])
      ]
    }
  ].filter(group => group.items.length > 0), [
    onPrevPage, onNextPage, canGoToPrev, canGoToNext,
    onZoomIn, onZoomOut, onRotate, onToggleFullscreen,
    onToggleSearch, onAddBookmark, onDownload, onOpenSidebar,
    scale
  ]);

  // Use provided toolbar groups or default ones
  const activeToolbarGroups = toolbarGroups || defaultToolbarGroups;

  // Separate groups by priority for adaptive rendering
  const primaryGroups = useMemo(() => 
    activeToolbarGroups.filter(g => g.priority === 'primary'),
    [activeToolbarGroups]
  );
  const secondaryGroups = useMemo(() => 
    activeToolbarGroups.filter(g => g.priority === 'secondary'),
    [activeToolbarGroups]
  );
  const utilityGroups = useMemo(() => 
    activeToolbarGroups.filter(g => g.priority === 'utility'),
    [activeToolbarGroups]
  );

  // Render toolbar group function
  const renderToolbarGroup = useCallback((group: ToolbarGroup, showLabels = false) => (
    <div key={group.id} className="flex items-center gap-1" data-testid={`toolbar-group-${group.id}`}>
      {group.items.map((item) => (
        <Tooltip key={item.id}>
          <TooltipTrigger asChild>
            <Button
              variant={item.active ? "default" : "ghost"}
              size={isCompact ? "sm" : "default"}
              onClick={item.action}
              disabled={item.disabled}
              className={cn(
                "relative h-8 w-8 p-0",
                item.active && "bg-accent text-accent-foreground"
              )}
            >
              <item.icon className="h-3 w-3" />
              {showLabels && !isCompact && (
                <span className="ml-2 hidden sm:inline">{item.label}</span>
              )}
            </Button>
          </TooltipTrigger>
          <TooltipContent>
            {item.tooltip || `${item.label}${item.shortcut ? ` (${item.shortcut})` : ''}`}
          </TooltipContent>
        </Tooltip>
      ))}
    </div>
  ), [isCompact]);

  // Page input handler for enhanced navigation
  const handlePageInputChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    if (!onPageChange || !numPages) return;
    const value = parseInt(e.target.value);
    if (!isNaN(value) && value >= 1 && value <= numPages) {
      onPageChange(value);
    }
  }, [onPageChange, numPages]);

  // Handle dragging
  const handleMouseDown = useCallback((e: React.MouseEvent) => {
    if (!dragHandleRef.current?.contains(e.target as Node)) return;

    setIsDragging(true);
    const rect = toolbarRef.current?.getBoundingClientRect();
    if (rect) {
      setDragOffset({
        x: e.clientX - rect.left,
        y: e.clientY - rect.top,
      });
    }
  }, []);

  const handleMouseMove = useCallback(
    (e: MouseEvent) => {
      if (!isDragging) return;

      const newPosition = {
        x: e.clientX - dragOffset.x,
        y: e.clientY - dragOffset.y,
      };

      // Keep toolbar within viewport bounds
      const toolbar = toolbarRef.current;
      if (toolbar) {
        const rect = toolbar.getBoundingClientRect();
        const maxX = window.innerWidth - rect.width;
        const maxY = window.innerHeight - rect.height;

        newPosition.x = Math.max(0, Math.min(maxX, newPosition.x));
        newPosition.y = Math.max(0, Math.min(maxY, newPosition.y));
      }

      onPositionChange(newPosition);
    },
    [isDragging, dragOffset, onPositionChange]
  );

  const handleMouseUp = useCallback(() => {
    setIsDragging(false);
  }, []);

  useEffect(() => {
    if (isDragging) {
      document.addEventListener("mousemove", handleMouseMove);
      document.addEventListener("mouseup", handleMouseUp);
      return () => {
        document.removeEventListener("mousemove", handleMouseMove);
        document.removeEventListener("mouseup", handleMouseUp);
      };
    }
  }, [isDragging, handleMouseMove, handleMouseUp]);

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.altKey) {
        const tool = ANNOTATION_TOOLS.find(
          (t) => t.shortcut.toLowerCase() === e.key.toLowerCase()
        );
        if (tool) {
          e.preventDefault();
          onToolSelect(selectedTool === tool.id ? null : tool.id);
        }
      }

      if (e.ctrlKey || e.metaKey) {
        switch (e.key) {
          case "z":
            if (e.shiftKey && onRedo && canRedo) {
              e.preventDefault();
              onRedo();
            } else if (onUndo && canUndo) {
              e.preventDefault();
              onUndo();
            }
            break;
          case "y":
            if (onRedo && canRedo) {
              e.preventDefault();
              onRedo();
            }
            break;
        }
      }

      if (e.key === "Escape") {
        onToolSelect(null);
        setShowColorPicker(false);
      }
    };

    document.addEventListener("keydown", handleKeyDown);
    return () => document.removeEventListener("keydown", handleKeyDown);
  }, [selectedTool, onToolSelect, onUndo, onRedo, canUndo, canRedo]);

  // Auto-hide when not in use
  useEffect(() => {
    let timeout: NodeJS.Timeout;
    if (!selectedTool && !showColorPicker) {
      timeout = setTimeout(() => {
        setIsExpanded(false);
      }, 3000);
    } else {
      setIsExpanded(true);
    }
    return () => clearTimeout(timeout);
  }, [selectedTool, showColorPicker]);

  if (!isVisible) return null;

  return (
    <TooltipProvider>
      <Card
        ref={toolbarRef}
        data-testid="floating-toolbar"
        className={cn(
          "fixed z-50 shadow-lg border-2 transition-all duration-300 select-none",
          isDragging && "cursor-grabbing shadow-2xl scale-105",
          !isExpanded && "opacity-75 hover:opacity-100",
          adaptiveLayout && "adaptive-toolbar",
          performanceMode && "performance-toolbar",
          className
        )}
        style={{
          left: `${position.x}px`,
          top: `${position.y}px`,
          transform: isDragging ? "rotate(2deg)" : "none",
        }}
        onMouseDown={handleMouseDown}
      >
        {/* Header with drag handle */}
        <div
          ref={dragHandleRef}
          className="flex items-center justify-between p-2 bg-muted/50 cursor-grab active:cursor-grabbing border-b"
        >
          <div className="flex items-center gap-2">
            <Move className="h-3 w-3 text-muted-foreground" />
            <span className="text-xs font-medium">Annotation Tools</span>
            {annotationCount > 0 && (
              <Badge variant="secondary" className="text-xs h-4">
                {annotationCount}
              </Badge>
            )}
          </div>

          <div className="flex items-center gap-1">
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-6 w-6 p-0"
                  onClick={() => setIsExpanded(!isExpanded)}
                >
                  {isExpanded ? (
                    <ChevronUp className="h-3 w-3" />
                  ) : (
                    <ChevronDown className="h-3 w-3" />
                  )}
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                {isExpanded ? "Collapse" : "Expand"}
              </TooltipContent>
            </Tooltip>

            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-6 w-6 p-0"
                  onClick={onToggleVisibility}
                >
                  <X className="h-3 w-3" />
                </Button>
              </TooltipTrigger>
              <TooltipContent>Hide Toolbar</TooltipContent>
            </Tooltip>
          </div>
        </div>

        {isExpanded && (
          <div className="p-3 space-y-3">
            {/* Enhanced Navigation Controls - Primary Groups */}
            {primaryGroups.length > 0 && (
              <>
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-1">
                    {primaryGroups.map(group => renderToolbarGroup(group))}
                  </div>
                  
                  {/* Page navigation display */}
                  {pageNumber && numPages && (
                    <div className="flex items-center gap-2 text-xs">
                      <input
                        type="number"
                        value={pageNumber}
                        onChange={handlePageInputChange}
                        min={1}
                        max={numPages}
                        className="w-12 text-center bg-background border border-border rounded px-1 py-0.5 text-xs focus:outline-none focus:ring-1 focus:ring-ring"
                      />
                      <span className="text-muted-foreground">/ {numPages}</span>
                    </div>
                  )}
                  
                  {/* Zoom indicator */}
                  {scale && (
                    <div className="flex items-center gap-1">
                      <span className="text-xs text-muted-foreground">
                        {Math.round(scale * 100)}%
                      </span>
                      {onResetZoom && (
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={onResetZoom}
                          className="h-6 px-2 text-xs"
                        >
                          Reset
                        </Button>
                      )}
                    </div>
                  )}
                </div>
                <Separator />
              </>
            )}

            {/* Main annotation tools */}
            <div className="grid grid-cols-4 gap-1">
              {ANNOTATION_TOOLS.map((tool) => {
                const Icon = tool.icon;
                const isActive = selectedTool === tool.id;

                return (
                  <Tooltip key={tool.id}>
                    <TooltipTrigger asChild>
                      <Button
                        variant={isActive ? "default" : "outline"}
                        size="sm"
                        className={cn(
                          "h-8 w-8 p-0 relative",
                          isActive && "ring-2 ring-primary/50"
                        )}
                        onClick={() => onToolSelect(isActive ? null : tool.id)}
                      >
                        <Icon className="h-3 w-3" />
                        {isActive && (
                          <div
                            className="absolute -bottom-1 -right-1 w-2 h-2 rounded-full border border-white"
                            style={{ backgroundColor: selectedColor }}
                          />
                        )}
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>
                      {tool.label} (Alt+{tool.shortcut})
                    </TooltipContent>
                  </Tooltip>
                );
              })}
            </div>

            <Separator />

            {/* Color picker and actions */}
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-1">
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant="outline"
                      size="sm"
                      className="h-8 w-8 p-0 relative"
                      onClick={() => setShowColorPicker(!showColorPicker)}
                    >
                      <Palette className="h-3 w-3" />
                      <div
                        className="absolute -bottom-1 -right-1 w-3 h-3 rounded-full border border-white"
                        style={{ backgroundColor: selectedColor }}
                      />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>Color Picker</TooltipContent>
                </Tooltip>

                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant="outline"
                      size="sm"
                      className="h-8 w-8 p-0"
                      onClick={onUndo}
                      disabled={!canUndo}
                    >
                      <Undo className="h-3 w-3" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>Undo (Ctrl+Z)</TooltipContent>
                </Tooltip>

                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant="outline"
                      size="sm"
                      className="h-8 w-8 p-0"
                      onClick={onRedo}
                      disabled={!canRedo}
                    >
                      <Redo className="h-3 w-3" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>Redo (Ctrl+Y)</TooltipContent>
                </Tooltip>
              </div>

              <div className="flex items-center gap-1">
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant="outline"
                      size="sm"
                      className="h-8 w-8 p-0"
                      onClick={() => onToolSelect(null)}
                    >
                      {selectedTool ? (
                        <EyeOff className="h-3 w-3" />
                      ) : (
                        <Eye className="h-3 w-3" />
                      )}
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>
                    {selectedTool
                      ? "Exit Annotation Mode"
                      : "Text Selection Mode"}
                  </TooltipContent>
                </Tooltip>

                {annotationCount > 0 && (
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        variant="outline"
                        size="sm"
                        className="h-8 w-8 p-0 text-destructive hover:text-destructive"
                        onClick={onClearAll}
                      >
                        <Trash2 className="h-3 w-3" />
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>Clear All Annotations</TooltipContent>
                  </Tooltip>
                )}

                {/* Overflow menu for secondary and utility groups */}
                {(secondaryGroups.length > 0 || utilityGroups.length > 0) && (
                  <ContextMenu>
                    <ContextMenuTrigger asChild>
                      <Button
                        variant="outline"
                        size="sm"
                        className="h-8 w-8 p-0"
                        onClick={() => setShowOverflowMenu(!showOverflowMenu)}
                      >
                        <MoreHorizontal className="h-3 w-3" />
                      </Button>
                    </ContextMenuTrigger>
                    <ContextMenuContent className="w-48">
                      {/* Secondary group items */}
                      {secondaryGroups.map(group => 
                        group.items.map(item => (
                          <ContextMenuItem
                            key={item.id}
                            onClick={item.action}
                            disabled={item.disabled}
                            className="flex items-center gap-2"
                          >
                            <item.icon className="h-4 w-4" />
                            {item.label}
                            {item.shortcut && (
                              <span className="ml-auto text-xs text-muted-foreground">
                                {item.shortcut}
                              </span>
                            )}
                          </ContextMenuItem>
                        ))
                      )}
                      
                      {/* Separator between secondary and utility */}
                      {secondaryGroups.length > 0 && utilityGroups.length > 0 && (
                        <ContextMenuSeparator />
                      )}
                      
                      {/* Utility group items */}
                      {utilityGroups.map(group =>
                        group.items.map(item => (
                          <ContextMenuItem
                            key={item.id}
                            onClick={item.action}
                            disabled={item.disabled}
                            className="flex items-center gap-2"
                          >
                            <item.icon className="h-4 w-4" />
                            {item.label}
                            {item.shortcut && (
                              <span className="ml-auto text-xs text-muted-foreground">
                                {item.shortcut}
                              </span>
                            )}
                          </ContextMenuItem>
                        ))
                      )}
                    </ContextMenuContent>
                  </ContextMenu>
                )}
              </div>
            </div>

            {/* Color palette */}
            {showColorPicker && (
              <>
                <Separator />
                <div className="space-y-2">
                  <div className="text-xs font-medium text-muted-foreground">
                    Colors
                  </div>
                  <div className="grid grid-cols-6 gap-1">
                    {COLOR_PALETTE.map((color) => (
                      <Tooltip key={color.value}>
                        <TooltipTrigger asChild>
                          <button
                            className={cn(
                              "w-6 h-6 rounded-full border-2 transition-all hover:scale-110",
                              selectedColor === color.value
                                ? "border-foreground ring-2 ring-primary/30"
                                : "border-muted-foreground/30 hover:border-foreground/50"
                            )}
                            style={{ backgroundColor: color.value }}
                            onClick={() => {
                              onColorChange(color.value);
                              setShowColorPicker(false);
                            }}
                          />
                        </TooltipTrigger>
                        <TooltipContent>{color.name}</TooltipContent>
                      </Tooltip>
                    ))}
                  </div>
                </div>
              </>
            )}

            {/* Active tool indicator */}
            {selectedTool && (
              <>
                <Separator />
                <div className="flex items-center justify-center gap-2 text-xs text-muted-foreground bg-muted/30 rounded p-2">
                  <div className="flex items-center gap-1">
                    {(() => {
                      const tool = ANNOTATION_TOOLS.find(
                        (t) => t.id === selectedTool
                      );
                      const Icon = tool?.icon || Pen;
                      return <Icon className="h-3 w-3" />;
                    })()}
                    <span className="capitalize font-medium">
                      {selectedTool}
                    </span>
                  </div>
                  <div
                    className="w-3 h-3 rounded-full border border-white"
                    style={{ backgroundColor: selectedColor }}
                  />
                  <span>Active</span>
                </div>
              </>
            )}
          </div>
        )}
      </Card>
    </TooltipProvider>
  );
}

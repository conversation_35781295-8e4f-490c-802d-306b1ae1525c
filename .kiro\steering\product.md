# Product Overview

## Cobalt PDF Viewer

A modern, feature-rich PDF viewer application built with Next.js and React. The application provides comprehensive PDF viewing capabilities with advanced features including:

- **Core PDF Viewing**: High-quality PDF rendering and display
- **Annotations**: Full annotation support with overlay functionality and export capabilities
- **Search**: Multiple search implementations (simple, enhanced, unified)
- **Forms**: PDF form designer, management, and validation
- **Navigation**: Sidebar, bookmarks, outline, thumbnails, and floating toolbars
- **Collaboration**: Real-time collaboration features
- **Accessibility**: Enhanced accessibility features for inclusive usage
- **Workflow Management**: Version control and workflow management tools
- **Advanced Tools**: OCR engine, image extraction, digital signatures, and performance monitoring

The application is designed as a comprehensive PDF solution suitable for both individual users and collaborative environments, with a focus on modern web standards and accessibility.
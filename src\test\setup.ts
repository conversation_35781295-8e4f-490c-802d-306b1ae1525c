import '@testing-library/jest-dom'
import { vi } from 'vitest'
import React from 'react'

// Mock react-pdf
vi.mock('react-pdf', () => ({
  Page: vi.fn().mockImplementation(({ children, ...props }: { children?: React.ReactNode; [key: string]: unknown }) => {
    return React.createElement('div', { 'data-testid': 'pdf-page', ...props }, children)
  }),
  Document: vi.fn().mockImplementation(({ children, ...props }: { children?: React.ReactNode; [key: string]: unknown }) => {
    return React.createElement('div', { 'data-testid': 'pdf-document', ...props }, children)
  }),
  pdfjs: {
    GlobalWorkerOptions: {
      workerSrc: '',
    },
  },
}))

// Mock next/navigation
vi.mock('next/navigation', () => ({
  useRouter: () => ({
    push: vi.fn(),
    replace: vi.fn(),
    back: vi.fn(),
  }),
  useSearchParams: () => new URLSearchParams(),
  usePathname: () => '/',
}))

// Mock sonner
vi.mock('sonner', () => ({
  toast: {
    success: vi.fn(),
    error: vi.fn(),
    info: vi.fn(),
    warning: vi.fn(),
  },
}))

// Global test utilities
global.ResizeObserver = vi.fn().mockImplementation(() => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn(),
}))

global.IntersectionObserver = vi.fn().mockImplementation(() => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn(),
}))
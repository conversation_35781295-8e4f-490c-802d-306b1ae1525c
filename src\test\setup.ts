import '@testing-library/jest-dom'
import { vi } from 'vitest'
import React from 'react'

// Mock the cn utility function
vi.mock('@/lib/utils', () => ({
  cn: (...classes: (string | undefined | null | boolean)[]) =>
    classes.filter(Boolean).join(' ')
}))

// Mock react-pdf
vi.mock('react-pdf', () => ({
  Page: vi.fn().mockImplementation(({ children, ...props }: { children?: React.ReactNode; [key: string]: unknown }) => {
    return React.createElement('div', { 'data-testid': 'pdf-page', ...props }, children)
  }),
  Document: vi.fn().mockImplementation(({ children, ...props }: { children?: React.ReactNode; [key: string]: unknown }) => {
    return React.createElement('div', { 'data-testid': 'pdf-document', ...props }, children)
  }),
  pdfjs: {
    GlobalWorkerOptions: {
      workerSrc: '',
    },
  },
}))

// Mock next/navigation
vi.mock('next/navigation', () => ({
  useRouter: () => ({
    push: vi.fn(),
    replace: vi.fn(),
    back: vi.fn(),
  }),
  useSearchParams: () => new URLSearchParams(),
  usePathname: () => '/',
}))

// Mock sonner
vi.mock('sonner', () => ({
  toast: {
    success: vi.fn(),
    error: vi.fn(),
    info: vi.fn(),
    warning: vi.fn(),
  },
}))

// Global test utilities
global.ResizeObserver = vi.fn().mockImplementation(() => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn(),
}))

global.IntersectionObserver = vi.fn().mockImplementation(() => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn(),
}))

// Mock TouchEvent for mobile tests
global.TouchEvent = vi.fn().mockImplementation((type, options = {}) => {
  const event = new Event(type, options)
  Object.defineProperty(event, 'touches', {
    value: options.touches || [],
    writable: false
  })
  Object.defineProperty(event, 'targetTouches', {
    value: options.targetTouches || [],
    writable: false
  })
  Object.defineProperty(event, 'changedTouches', {
    value: options.changedTouches || [],
    writable: false
  })
  return event
})

// Mock Touch constructor
global.Touch = vi.fn().mockImplementation((options = {}) => ({
  identifier: options.identifier || 0,
  target: options.target || null,
  clientX: options.clientX || 0,
  clientY: options.clientY || 0,
  pageX: options.pageX || 0,
  pageY: options.pageY || 0,
  screenX: options.screenX || 0,
  screenY: options.screenY || 0,
  radiusX: options.radiusX || 0,
  radiusY: options.radiusY || 0,
  rotationAngle: options.rotationAngle || 0,
  force: options.force || 1
}))
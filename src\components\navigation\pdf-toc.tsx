"use client"

import { <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/scroll-area"
import { Button } from "@/components/ui/button"
import { ChevronRight, ChevronDown, FileText } from "lucide-react"
import { useState } from "react"

interface OutlineItem {
  title: string
  page: number
  items?: OutlineItem[]
}

interface PDFTableOfContentsProps {
  outline: OutlineItem[]
  currentPage: number
  onPageSelect: (page: number) => void
}

export default function PDFTableOfContents({ outline, currentPage, onPageSelect }: PDFTableOfContentsProps) {
  const [expandedItems, setExpandedItems] = useState<Set<string>>(new Set())

  const toggleExpanded = (itemId: string) => {
    const newExpanded = new Set(expandedItems)
    if (newExpanded.has(itemId)) {
      newExpanded.delete(itemId)
    } else {
      newExpanded.add(itemId)
    }
    setExpandedItems(newExpanded)
  }

  const renderOutlineItem = (item: OutlineItem, index: number, level = 0) => {
    const itemId = `${level}-${index}`
    const hasChildren = item.items && item.items.length > 0
    const isExpanded = expandedItems.has(itemId)
    const isCurrentPage = item.page === currentPage

    return (
      <div key={itemId} className="select-none">
        <div
          className={`flex items-center gap-1 py-1 px-2 rounded-md cursor-pointer hover:bg-muted/50 ${
            isCurrentPage ? "bg-primary/10 text-primary" : ""
          }`}
          style={{ paddingLeft: `${level * 16 + 8}px` }}
          onClick={() => onPageSelect(item.page)}
        >
          {hasChildren && (
            <Button
              variant="ghost"
              size="sm"
              className="h-4 w-4 p-0"
              onClick={(e) => {
                e.stopPropagation()
                toggleExpanded(itemId)
              }}
            >
              {isExpanded ? <ChevronDown className="h-3 w-3" /> : <ChevronRight className="h-3 w-3" />}
            </Button>
          )}
          {!hasChildren && <div className="w-4" />}
          <FileText className="h-3 w-3 flex-shrink-0" />
          <span className="text-sm truncate flex-1" title={item.title}>
            {item.title}
          </span>
          <span className="text-xs text-muted-foreground">{item.page}</span>
        </div>

        {hasChildren && isExpanded && (
          <div>{item.items!.map((childItem, childIndex) => renderOutlineItem(childItem, childIndex, level + 1))}</div>
        )}
      </div>
    )
  }

  // Mock outline data if none provided
  const mockOutline: OutlineItem[] =
    outline.length > 0
      ? outline
      : [
          {
            title: "Introduction",
            page: 1,
            items: [
              { title: "Overview", page: 1 },
              { title: "Getting Started", page: 2 },
            ],
          },
          {
            title: "Main Content",
            page: 3,
            items: [
              { title: "Chapter 1", page: 3 },
              { title: "Chapter 2", page: 5 },
              { title: "Chapter 3", page: 8 },
            ],
          },
          {
            title: "Conclusion",
            page: 10,
          },
        ]

  return (
    <div className="h-full flex flex-col">
      <div className="p-4 border-b">
        <h3 className="font-semibold text-sm">Table of Contents</h3>
      </div>
      <ScrollArea className="flex-1">
        <div className="p-2">{mockOutline.map((item, index) => renderOutlineItem(item, index))}</div>
      </ScrollArea>
    </div>
  )
}

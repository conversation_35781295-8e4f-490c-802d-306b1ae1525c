# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

- `npm run dev` - Start development server with Turbopack
- `npm run build` - Build production version
- `npm run start` - Start production server
- `npm run lint` - Run ESLint

## Project Architecture

This is a Next.js 15 React PDF viewer application with a modular component architecture. The project uses TypeScript, Tailwind CSS, and react-pdf for PDF rendering.

### Component Organization

Components are organized in `/src/components/` with clear functional separation:

- **Core** (`/core/`) - Essential PDF viewing functionality (pdf-viewer, pdf-page-wrapper, pdf-upload)
  - **Hooks** (`/core/hooks/`) - Custom hooks for state management (usePDFNavigation, usePDFBookmarks, usePDFKeyboardShortcuts, useEnhancedViewerState)
- **Navigation** (`/navigation/`) - UI navigation components with consolidated enhanced features:
  - **Consolidated**: pdf-sidebar (with adaptive features), pdf-floating-toolbar (with optimization features), pdf-bookmarks, pdf-context-menu
  - **Enhanced**: GestureEnabledPage, FloatingActionButton, CommandPalette
- **Annotations** (`/annotations/`) - Annotation system with overlays, history, and export capabilities
- **Forms** (`/forms/`) - PDF form handling with designer, manager, overlay, and validation
- **Search** (`/search/`) - Consolidated search component with configurable modes (simple, enhanced, unified) and standalone enhanced search
- **Tools** (`/tools/`) - Utility features (OCR, digital signatures, text selection, print management)
- **Workflow** (`/workflow/`) - Version control, diff viewing, and workflow management
- **Accessibility** (`/accessibility/`) - Accessibility features
- **Collaboration** (`/collaboration/`) - Real-time collaboration features
- **UI** (`/ui/`) - Reusable UI components built with Radix UI primitives
- **Examples** (`/examples/`) - Implementation examples and demos

Each component directory has an `index.ts` file that exports all components for clean imports.

### Layout & Interaction Optimizations

The application has been fully optimized with a new layout system:

#### Optimized Toolbar
- **Logical grouping** - Primary (navigation, zoom), Secondary (tools), Utility (export) actions
- **Responsive design** - Adaptive layout with overflow menus for smaller screens
- **Smart organization** - Context-aware tool visibility and progressive disclosure
- **Performance** - Memoized actions and optimized re-rendering

#### Adaptive Sidebar
- **Mode-based filtering** - Shows relevant tabs based on current viewer mode (reading, annotating, form-filling, reviewing)
- **Contextual organization** - Collapsible categories with badge indicators
- **Mobile optimization** - Full-screen overlay on mobile with backdrop blur
- **State persistence** - Remembers expanded categories and active tabs

#### Enhanced Interactions
- **Gesture support** - Pinch-to-zoom, swipe navigation, double-tap actions
- **Touch optimization** - 44px minimum touch targets, momentum scrolling
- **Keyboard shortcuts** - Comprehensive command palette (Cmd+K) with fuzzy search
- **Floating actions** - Mode-specific quick action buttons with auto-hide

#### Advanced State Management
- **useEnhancedViewerState** - Centralized state with history/undo, preferences
- **Action-based updates** - Reducer pattern for complex state transitions
- **Smart persistence** - localStorage integration with error handling
- **Computed values** - Derived state for optimal performance

### Performance Optimizations

The codebase has been optimized for performance with the following improvements:

#### React Performance
- **React.memo** - Applied to expensive components like consolidated `PDFSimplePage`
- **useCallback** - All event handlers and functions passed as props are memoized
- **useMemo** - Computed values and filtered data are memoized
- **Custom Hooks** - Complex state logic extracted into reusable hooks
- **State Management** - Reducer pattern for complex interactions with history

#### UI Performance
- **Specific Transitions** - Replaced `transition-all` with specific property transitions
- **Hardware Acceleration** - `transform-gpu` and `will-change` for smooth animations
- **Gesture Optimization** - Passive event listeners and efficient touch handling
- **Lazy Loading** - Component-level code splitting where appropriate

#### Layout Performance
- **Adaptive Rendering** - Components show/hide based on screen size and mode
- **Virtual Scrolling** - Efficient rendering for large lists (thumbnails, results)
- **Smart Re-rendering** - Optimized dependency arrays and memo comparisons
- **Resource Management** - Proper cleanup and memory management

#### Mobile Responsiveness
- **Touch-First Design** - 44px minimum touch targets for mobile
- **Gesture Support** - Native touch gestures (pinch, swipe, pan)
- **Responsive Breakpoints** - Components adapt across screen sizes
- **Safe Areas** - Support for device safe areas and notches
- **Momentum Scrolling** - iOS-style smooth scrolling with overscroll containment

### Key Dependencies

- **react-pdf** - PDF rendering and document handling
- **Radix UI** - Accessible component primitives for UI elements
- **Lucide React** - Icon library
- **next-themes** - Theme management
- **Tailwind CSS** - Styling with CVA for component variants
- **Sonner** - Toast notifications

### TypeScript Configuration

- Uses `@/*` path alias for clean imports from `src/`
- Strict TypeScript configuration with ES2017 target
- Next.js plugin integration for enhanced TypeScript support

### Usage Examples

See `/src/components/examples/optimized-pdf-viewer.tsx` for a complete implementation example using all optimized components.

### Development Notes

- Uses Turbopack for faster development builds
- Component exports are centralized through `src/components/index.ts`
- The project follows a feature-based component organization pattern
- Theme support is integrated via ThemeProvider component
- Performance-first approach with memoization and optimization patterns
- Mobile-first responsive design with touch-friendly interactions
- Comprehensive gesture support for modern interaction patterns
- Advanced state management with history and preferences
- Command palette for power-user workflows

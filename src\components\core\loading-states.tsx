"use client";

import React, { useState, useEffect } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Button } from '@/components/ui/button';
import { 
  Loader2, 
  FileText, 
  Download, 
  CheckCircle, 
  AlertCircle,
  X,
  Clock,
  Zap
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface LoadingStateProps {
  type: 'document' | 'page' | 'thumbnail' | 'search' | 'annotation';
  progress?: number;
  message?: string;
  subMessage?: string;
  showCancel?: boolean;
  onCancel?: () => void;
  className?: string;
  size?: 'sm' | 'md' | 'lg';
  variant?: 'default' | 'minimal' | 'detailed';
}

interface ProgressIndicatorProps {
  progress: number;
  total?: number;
  label?: string;
  showPercentage?: boolean;
  showEta?: boolean;
  startTime?: number;
  className?: string;
}

interface DocumentLoadingProps {
  documentTitle: string;
  progress?: number;
  stage: 'downloading' | 'parsing' | 'rendering' | 'complete';
  onCancel?: () => void;
  estimatedSize?: number;
  loadedSize?: number;
}

// Enhanced progress indicator with ETA calculation
export const ProgressIndicator: React.FC<ProgressIndicatorProps> = ({
  progress,
  total,
  label,
  showPercentage = true,
  showEta = false,
  startTime,
  className
}) => {
  const [eta, setEta] = useState<string>('');

  useEffect(() => {
    if (showEta && startTime && progress > 0 && progress < 100) {
      const elapsed = Date.now() - startTime;
      const rate = progress / elapsed;
      const remaining = (100 - progress) / rate;
      
      if (remaining > 0 && remaining < Infinity) {
        const seconds = Math.round(remaining / 1000);
        if (seconds < 60) {
          setEta(`${seconds}s remaining`);
        } else {
          const minutes = Math.floor(seconds / 60);
          setEta(`${minutes}m ${seconds % 60}s remaining`);
        }
      }
    }
  }, [progress, startTime, showEta]);

  return (
    <div className={cn("space-y-2", className)}>
      {label && (
        <div className="flex justify-between items-center text-sm">
          <span className="font-medium">{label}</span>
          {showPercentage && (
            <span className="text-muted-foreground">{Math.round(progress)}%</span>
          )}
        </div>
      )}
      
      <Progress value={progress} className="h-2" />
      
      {showEta && eta && (
        <div className="flex justify-between items-center text-xs text-muted-foreground">
          <span>{eta}</span>
          {total && (
            <span>{Math.round(progress * total / 100)} / {total}</span>
          )}
        </div>
      )}
    </div>
  );
};

// Document-specific loading component
export const DocumentLoading: React.FC<DocumentLoadingProps> = ({
  documentTitle,
  progress = 0,
  stage,
  onCancel,
  estimatedSize,
  loadedSize
}) => {
  const [startTime] = useState(Date.now());

  const getStageInfo = () => {
    switch (stage) {
      case 'downloading':
        return {
          icon: <Download className="h-5 w-5 animate-pulse" />,
          title: 'Downloading PDF',
          description: 'Fetching document from server...'
        };
      case 'parsing':
        return {
          icon: <FileText className="h-5 w-5 animate-pulse" />,
          title: 'Processing PDF',
          description: 'Analyzing document structure...'
        };
      case 'rendering':
        return {
          icon: <Zap className="h-5 w-5 animate-pulse" />,
          title: 'Rendering Pages',
          description: 'Preparing document for viewing...'
        };
      case 'complete':
        return {
          icon: <CheckCircle className="h-5 w-5 text-green-500" />,
          title: 'Document Ready',
          description: 'Successfully loaded!'
        };
    }
  };

  const stageInfo = getStageInfo();

  return (
    <Card className="max-w-md mx-auto">
      <CardContent className="p-6">
        <div className="text-center space-y-4">
          <div className="flex justify-center">
            {stageInfo.icon}
          </div>
          
          <div className="space-y-2">
            <h3 className="font-semibold">{stageInfo.title}</h3>
            <p className="text-sm text-muted-foreground">{stageInfo.description}</p>
            <p className="text-xs font-medium truncate">{documentTitle}</p>
          </div>

          {stage !== 'complete' && (
            <ProgressIndicator
              progress={progress}
              label={stage === 'downloading' ? 'Download Progress' : 'Processing'}
              showEta={stage === 'downloading'}
              startTime={startTime}
            />
          )}

          {estimatedSize && loadedSize && stage === 'downloading' && (
            <div className="text-xs text-muted-foreground">
              {(loadedSize / 1024 / 1024).toFixed(1)} MB / {(estimatedSize / 1024 / 1024).toFixed(1)} MB
            </div>
          )}

          {onCancel && stage !== 'complete' && (
            <Button variant="outline" size="sm" onClick={onCancel}>
              <X className="h-3 w-3 mr-1" />
              Cancel
            </Button>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

// Generic loading state component
export const LoadingState: React.FC<LoadingStateProps> = ({
  type,
  progress,
  message,
  subMessage,
  showCancel = false,
  onCancel,
  className,
  size = 'md',
  variant = 'default'
}) => {
  const getTypeInfo = () => {
    switch (type) {
      case 'document':
        return {
          icon: <FileText className="h-5 w-5" />,
          defaultMessage: 'Loading document...'
        };
      case 'page':
        return {
          icon: <Loader2 className="h-4 w-4 animate-spin" />,
          defaultMessage: 'Rendering page...'
        };
      case 'thumbnail':
        return {
          icon: <Loader2 className="h-3 w-3 animate-spin" />,
          defaultMessage: 'Generating thumbnail...'
        };
      case 'search':
        return {
          icon: <Loader2 className="h-4 w-4 animate-spin" />,
          defaultMessage: 'Searching document...'
        };
      case 'annotation':
        return {
          icon: <Loader2 className="h-4 w-4 animate-spin" />,
          defaultMessage: 'Processing annotation...'
        };
    }
  };

  const typeInfo = getTypeInfo();
  const displayMessage = message || typeInfo.defaultMessage;

  const sizeClasses = {
    sm: 'p-2 text-xs',
    md: 'p-4 text-sm',
    lg: 'p-6 text-base'
  };

  if (variant === 'minimal') {
    return (
      <div className={cn("flex items-center gap-2", className)}>
        {typeInfo.icon}
        <span className="text-sm text-muted-foreground">{displayMessage}</span>
      </div>
    );
  }

  if (variant === 'detailed') {
    return (
      <Card className={cn("", className)}>
        <CardContent className={sizeClasses[size]}>
          <div className="space-y-3">
            <div className="flex items-center gap-3">
              {typeInfo.icon}
              <div className="flex-1">
                <p className="font-medium">{displayMessage}</p>
                {subMessage && (
                  <p className="text-xs text-muted-foreground mt-1">{subMessage}</p>
                )}
              </div>
            </div>

            {typeof progress === 'number' && (
              <ProgressIndicator progress={progress} showPercentage />
            )}

            {showCancel && onCancel && (
              <Button variant="outline" size="sm" onClick={onCancel} className="w-full">
                Cancel
              </Button>
            )}
          </div>
        </CardContent>
      </Card>
    );
  }

  // Default variant
  return (
    <div className={cn("flex items-center justify-center", sizeClasses[size], className)}>
      <div className="text-center space-y-2">
        <div className="flex justify-center">
          {typeInfo.icon}
        </div>
        <p className="text-muted-foreground">{displayMessage}</p>
        {subMessage && (
          <p className="text-xs text-muted-foreground">{subMessage}</p>
        )}
        {typeof progress === 'number' && (
          <div className="w-32 mx-auto">
            <Progress value={progress} className="h-1" />
          </div>
        )}
      </div>
    </div>
  );
};

// Skeleton loader for PDF pages
export const PageSkeleton: React.FC<{ className?: string }> = ({ className }) => (
  <div className={cn("animate-pulse", className)}>
    <div className="bg-muted rounded-lg aspect-[8.5/11] flex items-center justify-center">
      <FileText className="h-12 w-12 text-muted-foreground/50" />
    </div>
  </div>
);

// Skeleton loader for thumbnails
export const ThumbnailSkeleton: React.FC<{ className?: string }> = ({ className }) => (
  <div className={cn("animate-pulse", className)}>
    <div className="bg-muted rounded aspect-[8.5/11] flex items-center justify-center">
      <FileText className="h-6 w-6 text-muted-foreground/50" />
    </div>
  </div>
);

// Loading overlay for the entire viewer
export const ViewerLoadingOverlay: React.FC<{
  message?: string;
  progress?: number;
  onCancel?: () => void;
}> = ({ message = 'Loading...', progress, onCancel }) => (
  <div className="absolute inset-0 bg-background/80 backdrop-blur-sm flex items-center justify-center z-50">
    <LoadingState
      type="document"
      message={message}
      progress={progress}
      showCancel={!!onCancel}
      onCancel={onCancel}
      variant="detailed"
      size="lg"
    />
  </div>
);

export default LoadingState;

@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

@theme inline {
  /* Responsive breakpoints for mobile-first design */
  --breakpoint-xs: 320px;
  --breakpoint-sm: 375px;
  --breakpoint-md: 414px;
  --breakpoint-lg: 768px;
  --breakpoint-xl: 1024px;
  --breakpoint-2xl: 1280px;

  /* Touch target sizes for mobile accessibility */
  --touch-target-min: 44px;
  --touch-target-comfortable: 48px;

  /* Mobile-specific spacing */
  --mobile-padding: 1rem;
  --mobile-gap: 0.75rem;

  /* Color system */
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

:root {
  --radius: 0.625rem;
  --background: oklch(1 0 0);
  --foreground: oklch(0.145 0 0);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.145 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.145 0 0);
  --primary: oklch(0.205 0 0);
  --primary-foreground: oklch(0.985 0 0);
  --secondary: oklch(0.97 0 0);
  --secondary-foreground: oklch(0.205 0 0);
  --muted: oklch(0.97 0 0);
  --muted-foreground: oklch(0.556 0 0);
  --accent: oklch(0.97 0 0);
  --accent-foreground: oklch(0.205 0 0);
  --destructive: oklch(0.577 0.245 27.325);
  --border: oklch(0.922 0 0);
  --input: oklch(0.922 0 0);
  --ring: oklch(0.708 0 0);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.145 0 0);
  --sidebar-primary: oklch(0.205 0 0);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.97 0 0);
  --sidebar-accent-foreground: oklch(0.205 0 0);
  --sidebar-border: oklch(0.922 0 0);
  --sidebar-ring: oklch(0.708 0 0);
}

.dark {
  --background: oklch(0.145 0 0);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.205 0 0);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.205 0 0);
  --popover-foreground: oklch(0.985 0 0);
  --primary: oklch(0.922 0 0);
  --primary-foreground: oklch(0.205 0 0);
  --secondary: oklch(0.269 0 0);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.269 0 0);
  --muted-foreground: oklch(0.708 0 0);
  --accent: oklch(0.269 0 0);
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.704 0.191 22.216);
  --border: oklch(1 0 0 / 10%);
  --input: oklch(1 0 0 / 15%);
  --ring: oklch(0.556 0 0);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.205 0 0);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.269 0 0);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(1 0 0 / 10%);
  --sidebar-ring: oklch(0.556 0 0);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
    /* Prevent horizontal scrolling on mobile */
    overflow-x: hidden;
    /* Enable momentum scrolling on iOS */
    -webkit-overflow-scrolling: touch;
  }

  /* Ensure touch targets meet accessibility requirements */
  button,
  [role="button"],
  input[type="button"],
  input[type="submit"],
  input[type="reset"] {
    min-height: var(--touch-target-min);
    min-width: var(--touch-target-min);
  }

  /* Improve text readability on mobile */
  @media (max-width: 768px) {
    body {
      font-size: 16px; /* Prevent zoom on iOS */
      line-height: 1.5;
    }

    /* Optimize input fields for mobile */
    input, textarea, select {
      font-size: 16px; /* Prevent zoom on iOS */
    }
  }
}

@layer utilities {
  /* Mobile-first responsive utilities */
  .touch-target {
    min-height: var(--touch-target-min);
    min-width: var(--touch-target-min);
  }

  .touch-target-comfortable {
    min-height: var(--touch-target-comfortable);
    min-width: var(--touch-target-comfortable);
  }

  /* Mobile-specific spacing */
  .mobile-padding {
    padding: var(--mobile-padding);
  }

  .mobile-gap {
    gap: var(--mobile-gap);
  }

  /* Prevent layout shift */
  .prevent-layout-shift {
    contain: layout style paint;
  }

  /* Safe area support for devices with notches */
  .safe-area-top {
    padding-top: env(safe-area-inset-top);
  }

  .safe-area-bottom {
    padding-bottom: env(safe-area-inset-bottom);
  }

  .safe-area-left {
    padding-left: env(safe-area-inset-left);
  }

  .safe-area-right {
    padding-right: env(safe-area-inset-right);
  }

  /* Mobile-optimized scrolling */
  .mobile-scroll {
    -webkit-overflow-scrolling: touch;
    overscroll-behavior: contain;
  }

  /* Performance optimizations for mobile */
  .mobile-optimized {
    /* Hardware acceleration */
    transform: translateZ(0);
    will-change: transform;

    /* Optimize rendering */
    backface-visibility: hidden;
    perspective: 1000px;
  }

  /* PDF rendering optimizations */
  .pdf-page-mobile {
    /* Optimize image rendering */
    image-rendering: optimizeQuality;
    image-rendering: -webkit-optimize-contrast;

    /* Prevent layout shifts */
    contain: layout style paint;

    /* Smooth transitions */
    transition: transform 0.2s ease-out;
  }

  /* Reduce motion for users who prefer it */
  @media (prefers-reduced-motion: reduce) {
    .mobile-optimized,
    .pdf-page-mobile {
      transition: none;
      animation: none;
      will-change: auto;
    }
  }

  /* High DPI display optimizations */
  @media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .pdf-page-mobile {
      image-rendering: -webkit-optimize-contrast;
      image-rendering: crisp-edges;
    }
  }

  /* Layout optimization styles */
  .layout-optimized {
    /* Compact spacing */
    --spacing-xs: 0.125rem;
    --spacing-sm: 0.25rem;
    --spacing-md: 0.375rem;
    --spacing-lg: 0.5rem;
    --spacing-xl: 0.75rem;
  }

  .layout-optimized .compact-header {
    min-height: 2.5rem;
    padding: 0.25rem 0.5rem;
  }

  .layout-optimized .compact-tabs {
    min-height: 2rem;
  }

  .layout-optimized .compact-sidebar {
    width: 16rem;
    transition: width 0.2s ease-in-out;
  }

  .layout-optimized .compact-sidebar.mini {
    width: 3rem;
  }

  .layout-optimized .compact-sidebar.hidden {
    width: 0;
    overflow: hidden;
  }

  /* PDF display area optimizations */
  .pdf-display-optimized {
    /* Remove unnecessary margins and padding */
    margin: 0;
    padding: 0.25rem;
  }

  .pdf-display-optimized .pdf-page {
    /* Optimize page rendering */
    contain: layout style paint;
    will-change: transform;
  }

  /* Multi-document layout styles */
  .multi-pdf-container {
    display: grid;
    gap: 0.25rem;
    height: 100%;
  }

  .multi-pdf-container.side-by-side {
    grid-template-columns: 1fr 1fr;
  }

  .multi-pdf-container.grid-2x2 {
    grid-template-columns: 1fr 1fr;
    grid-template-rows: 1fr 1fr;
  }

  .multi-pdf-container.grid-3x3 {
    grid-template-columns: repeat(3, 1fr);
    grid-template-rows: repeat(3, 1fr);
  }

  /* Responsive breakpoint styles */
  @media (max-width: 767px) {
    .layout-optimized {
      --spacing-xs: 0.0625rem;
      --spacing-sm: 0.125rem;
      --spacing-md: 0.25rem;
      --spacing-lg: 0.375rem;
      --spacing-xl: 0.5rem;
    }

    .layout-optimized .compact-header {
      min-height: 2rem;
      padding: 0.125rem 0.25rem;
    }

    .layout-optimized .compact-tabs {
      min-height: 1.75rem;
    }

    .multi-pdf-container.side-by-side {
      grid-template-columns: 1fr;
      grid-template-rows: 1fr 1fr;
    }

    .multi-pdf-container.grid-2x2,
    .multi-pdf-container.grid-3x3 {
      grid-template-columns: 1fr;
      grid-template-rows: repeat(auto-fit, 1fr);
    }
  }

  @media (min-width: 768px) and (max-width: 1023px) {
    .layout-optimized .compact-sidebar {
      width: 14rem;
    }
  }

  /* Desktop optimizations (1366px+) */
  @media (min-width: 1366px) {
    .layout-optimized .compact-sidebar {
      width: 18rem;
    }

    .layout-optimized .compact-header {
      min-height: 3rem;
      padding: 0.5rem 1rem;
    }

    .pdf-display-optimized {
      padding: 0.5rem;
    }

    /* Better space utilization for desktop */
    .multi-pdf-container {
      gap: 0.5rem;
    }
  }

  /* Large desktop optimizations (1920px+) */
  @media (min-width: 1920px) {
    .layout-optimized .compact-sidebar {
      width: 22rem;
    }

    .multi-pdf-container.grid-3x3 {
      grid-template-columns: repeat(3, 1fr);
      grid-template-rows: repeat(3, 1fr);
    }

    .multi-pdf-container.grid-4x3 {
      grid-template-columns: repeat(4, 1fr);
      grid-template-rows: repeat(3, 1fr);
    }

    /* Larger content area for high-resolution displays */
    .pdf-display-optimized {
      padding: 0.75rem;
    }
  }

  /* Ultra-wide optimizations (2560px+) */
  @media (min-width: 2560px) {
    .layout-optimized .compact-sidebar {
      width: 24rem;
    }

    .multi-pdf-container.grid-4x4 {
      grid-template-columns: repeat(4, 1fr);
      grid-template-rows: repeat(4, 1fr);
    }

    .multi-pdf-container.grid-6x2 {
      grid-template-columns: repeat(6, 1fr);
      grid-template-rows: repeat(2, 1fr);
    }
  }

  /* Animation optimizations */
  .layout-optimized .animate-fast {
    transition-duration: 0.15s;
  }

  .layout-optimized .animate-normal {
    transition-duration: 0.2s;
  }

  .layout-optimized .animate-slow {
    transition-duration: 0.3s;
  }

  /* Accessibility improvements */
  .layout-optimized .focus-visible {
    outline: 2px solid var(--primary);
    outline-offset: 2px;
  }

  .layout-optimized .high-contrast {
    --background: #000000;
    --foreground: #ffffff;
    --muted: #333333;
    --muted-foreground: #cccccc;
    --border: #666666;
  }

  /* Performance optimizations */
  .layout-optimized .gpu-accelerated {
    transform: translateZ(0);
    will-change: transform;
    backface-visibility: hidden;
  }

  .layout-optimized .contain-layout {
    contain: layout style paint;
  }

  /* Scrollbar optimizations */
  .layout-optimized .scrollbar-thin {
    scrollbar-width: thin;
  }

  .layout-optimized .scrollbar-thin::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  .layout-optimized .scrollbar-thin::-webkit-scrollbar-track {
    background: transparent;
  }

  .layout-optimized .scrollbar-thin::-webkit-scrollbar-thumb {
    background: var(--muted-foreground);
    border-radius: 3px;
  }

  .layout-optimized .scrollbar-thin::-webkit-scrollbar-thumb:hover {
    background: var(--foreground);
  }
}

import { useState, useCallback } from 'react'

interface UsePDFNavigationProps {
  initialScale?: number
  initialRotation?: number
  maxScale?: number
  minScale?: number
  scaleStep?: number
}

export const usePDFNavigation = ({
  initialScale = 1.0,
  initialRotation = 0,
  maxScale = 3.0,
  minScale = 0.5,
  scaleStep = 0.25
}: UsePDFNavigationProps = {}) => {
  const [pageNumber, setPageNumber] = useState(1)
  const [numPages, setNumPages] = useState(0)
  const [scale, setScale] = useState(initialScale)
  const [rotation, setRotation] = useState(initialRotation)

  const goToPrevPage = useCallback(() => {
    setPageNumber((prev) => Math.max(1, prev - 1))
  }, [])

  const goToNextPage = useCallback(() => {
    setPageNumber((prev) => Math.min(numPages, prev + 1))
  }, [numPages])

  const goToPage = useCallback((page: number) => {
    if (page >= 1 && page <= numPages) {
      setPageNumber(page)
    }
  }, [numPages])

  const zoomIn = useCallback(() => {
    setScale((prev) => Math.min(maxScale, prev + scaleStep))
  }, [maxScale, scaleStep])

  const zoomOut = useCallback(() => {
    setScale((prev) => Math.max(minScale, prev - scaleStep))
  }, [minScale, scaleStep])

  const resetZoom = useCallback(() => {
    setScale(initialScale)
  }, [initialScale])

  const rotate = useCallback(() => {
    setRotation((prev) => (prev + 90) % 360)
  }, [])

  const canGoToPrev = pageNumber > 1
  const canGoToNext = pageNumber < numPages

  return {
    // State
    pageNumber,
    numPages,
    scale,
    rotation,
    
    // Navigation actions
    goToPrevPage,
    goToNextPage,
    goToPage,
    
    // Zoom actions
    zoomIn,
    zoomOut,
    resetZoom,
    
    // Rotation
    rotate,
    
    // Helpers
    canGoToPrev,
    canGoToNext,
    
    // State setters (for document load)
    setNumPages,
    setPageNumber,
    setScale,
    setRotation,
  }
}
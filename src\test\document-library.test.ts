import { describe, it, expect, beforeEach, vi } from 'vitest';
import { DocumentLibraryStorage } from '../lib/document-library';
import { createDefaultDocumentMetadata } from '../lib/types/pdf';

// Mock IndexedDB
const mockIndexedDB = {
  open: vi.fn(),
  deleteDatabase: vi.fn(),
};

const mockIDBDatabase = {
  createObjectStore: vi.fn(),
  transaction: vi.fn(),
  close: vi.fn(),
};

const mockIDBTransaction = {
  objectStore: vi.fn(),
  oncomplete: null,
  onerror: null,
};

const mockIDBObjectStore = {
  add: vi.fn(),
  get: vi.fn(),
  put: vi.fn(),
  delete: vi.fn(),
  getAll: vi.fn(),
  createIndex: vi.fn(),
};

const mockIDBRequest = {
  onsuccess: null,
  onerror: null,
  result: null,
};

// Setup global mocks
global.indexedDB = mockIndexedDB as any;
global.IDBDatabase = mockIDBDatabase as any;
global.IDBTransaction = mockIDBTransaction as any;
global.IDBObjectStore = mockIDBObjectStore as any;
global.IDBRequest = mockIDBRequest as any;

describe('DocumentLibraryStorage', () => {
  let documentLibrary: DocumentLibraryStorage;

  beforeEach(() => {
    documentLibrary = new DocumentLibraryStorage();
    vi.clearAllMocks();
  });

  describe('Document Metadata', () => {
    it('should create default metadata for a file', () => {
      const file = new File(['test content'], 'test.pdf', { type: 'application/pdf' });
      const metadata = createDefaultDocumentMetadata(file);

      expect(metadata.title).toBe('test');
      expect(metadata.fileName).toBe('test.pdf');
      expect(metadata.fileSize).toBe(file.size);
      expect(metadata.mimeType).toBe('application/pdf');
      expect(metadata.tags).toEqual([]);
      expect(metadata.categories).toEqual([]);
      expect(metadata.isFavorite).toBe(false);
      expect(metadata.isPinned).toBe(false);
      expect(metadata.openCount).toBe(0);
    });

    it('should create default metadata for a URL', () => {
      const url = 'https://example.com/document.pdf';
      const metadata = createDefaultDocumentMetadata(url);

      expect(metadata.title).toBe('document');
      expect(metadata.fileName).toBe('document.pdf');
      expect(metadata.fileSize).toBe(0);
      expect(metadata.mimeType).toBe('application/pdf');
    });
  });

  describe('Document Storage', () => {
    it('should initialize the database', async () => {
      const mockOpenRequest = {
        onsuccess: null,
        onerror: null,
        onupgradeneeded: null,
        result: mockIDBDatabase,
      };

      mockIndexedDB.open.mockReturnValue(mockOpenRequest);

      const initPromise = documentLibrary.initialize();

      // Simulate successful database opening
      if (mockOpenRequest.onsuccess) {
        mockOpenRequest.onsuccess({ target: mockOpenRequest } as any);
      }

      await expect(initPromise).resolves.toBeUndefined();
      expect(mockIndexedDB.open).toHaveBeenCalledWith('PDFDocumentLibrary', 1);
    });

    it('should handle database initialization errors', async () => {
      const mockOpenRequest = {
        onsuccess: null,
        onerror: null,
        onupgradeneeded: null,
        result: null,
      };

      mockIndexedDB.open.mockReturnValue(mockOpenRequest);

      const initPromise = documentLibrary.initialize();

      // Simulate database error
      if (mockOpenRequest.onerror) {
        mockOpenRequest.onerror(new Error('Database error') as any);
      }

      await expect(initPromise).rejects.toThrow('Failed to open IndexedDB');
    });
  });

  describe('Document Operations', () => {
    beforeEach(async () => {
      // Mock successful initialization
      const mockOpenRequest = {
        onsuccess: null,
        onerror: null,
        onupgradeneeded: null,
        result: mockIDBDatabase,
      };

      mockIndexedDB.open.mockReturnValue(mockOpenRequest);
      const initPromise = documentLibrary.initialize();
      
      if (mockOpenRequest.onsuccess) {
        mockOpenRequest.onsuccess({ target: mockOpenRequest } as any);
      }
      
      await initPromise;
    });

    it('should add a document to the library', async () => {
      const file = new File(['test content'], 'test.pdf', { type: 'application/pdf' });
      
      // Mock transaction and store
      const mockAddRequest = { ...mockIDBRequest };
      mockIDBObjectStore.add.mockReturnValue(mockAddRequest);
      mockIDBTransaction.objectStore.mockReturnValue(mockIDBObjectStore);
      mockIDBDatabase.transaction.mockReturnValue(mockIDBTransaction);

      const addPromise = documentLibrary.addDocument(file);

      // Simulate successful add
      if (mockAddRequest.onsuccess) {
        mockAddRequest.onsuccess({} as any);
      }

      const document = await addPromise;

      expect(document.title).toBe('test');
      expect(document.file).toBe(file);
      expect(document.metadata.fileName).toBe('test.pdf');
      expect(mockIDBObjectStore.add).toHaveBeenCalled();
    });

    it('should get a document by ID', async () => {
      const documentId = 'test-doc-id';
      const mockDocument = {
        id: documentId,
        title: 'Test Document',
        file: new File(['test'], 'test.pdf'),
        metadata: createDefaultDocumentMetadata('test.pdf'),
      };

      // Mock transaction and store
      const mockGetRequest = { ...mockIDBRequest, result: mockDocument };
      mockIDBObjectStore.get.mockReturnValue(mockGetRequest);
      mockIDBTransaction.objectStore.mockReturnValue(mockIDBObjectStore);
      mockIDBDatabase.transaction.mockReturnValue(mockIDBTransaction);

      const getPromise = documentLibrary.getDocument(documentId);

      // Simulate successful get
      if (mockGetRequest.onsuccess) {
        mockGetRequest.onsuccess({} as any);
      }

      const result = await getPromise;

      expect(result).toEqual(mockDocument);
      expect(mockIDBObjectStore.get).toHaveBeenCalledWith(documentId);
    });

    it('should return null for non-existent document', async () => {
      const documentId = 'non-existent-id';

      // Mock transaction and store
      const mockGetRequest = { ...mockIDBRequest, result: undefined };
      mockIDBObjectStore.get.mockReturnValue(mockGetRequest);
      mockIDBTransaction.objectStore.mockReturnValue(mockIDBObjectStore);
      mockIDBDatabase.transaction.mockReturnValue(mockIDBTransaction);

      const getPromise = documentLibrary.getDocument(documentId);

      // Simulate successful get with no result
      if (mockGetRequest.onsuccess) {
        mockGetRequest.onsuccess({} as any);
      }

      const result = await getPromise;

      expect(result).toBeNull();
    });

    it('should update a document', async () => {
      const documentId = 'test-doc-id';
      const existingDocument = {
        id: documentId,
        title: 'Test Document',
        file: new File(['test'], 'test.pdf'),
        metadata: createDefaultDocumentMetadata('test.pdf'),
      };

      // Mock get request
      const mockGetRequest = { ...mockIDBRequest, result: existingDocument };
      mockIDBObjectStore.get.mockReturnValue(mockGetRequest);

      // Mock put request
      const mockPutRequest = { ...mockIDBRequest };
      mockIDBObjectStore.put.mockReturnValue(mockPutRequest);

      mockIDBTransaction.objectStore.mockReturnValue(mockIDBObjectStore);
      mockIDBDatabase.transaction.mockReturnValue(mockIDBTransaction);

      const updatePromise = documentLibrary.updateDocument(documentId, {
        title: 'Updated Title'
      });

      // Simulate successful get
      if (mockGetRequest.onsuccess) {
        mockGetRequest.onsuccess({} as any);
      }

      // Simulate successful put
      if (mockPutRequest.onsuccess) {
        mockPutRequest.onsuccess({} as any);
      }

      await updatePromise;

      expect(mockIDBObjectStore.get).toHaveBeenCalledWith(documentId);
      expect(mockIDBObjectStore.put).toHaveBeenCalled();
    });

    it('should remove a document', async () => {
      const documentId = 'test-doc-id';

      // Mock transaction and store
      const mockDeleteRequest = { ...mockIDBRequest };
      mockIDBObjectStore.delete.mockReturnValue(mockDeleteRequest);
      mockIDBTransaction.objectStore.mockReturnValue(mockIDBObjectStore);
      mockIDBDatabase.transaction.mockReturnValue(mockIDBTransaction);

      const deletePromise = documentLibrary.removeDocument(documentId);

      // Simulate successful delete
      if (mockDeleteRequest.onsuccess) {
        mockDeleteRequest.onsuccess({} as any);
      }

      await deletePromise;

      expect(mockIDBObjectStore.delete).toHaveBeenCalledWith(documentId);
    });
  });

  describe('Search Functionality', () => {
    it('should search documents by text', async () => {
      const documents = [
        {
          id: '1',
          title: 'JavaScript Guide',
          metadata: {
            title: 'JavaScript Guide',
            author: 'John Doe',
            tags: ['programming', 'javascript'],
            categories: ['tutorials'],
          },
        },
        {
          id: '2',
          title: 'Python Basics',
          metadata: {
            title: 'Python Basics',
            author: 'Jane Smith',
            tags: ['programming', 'python'],
            categories: ['tutorials'],
          },
        },
      ];

      // Mock getAllDocuments
      const mockGetAllRequest = { ...mockIDBRequest, result: documents };
      mockIDBObjectStore.getAll.mockReturnValue(mockGetAllRequest);
      mockIDBTransaction.objectStore.mockReturnValue(mockIDBObjectStore);
      mockIDBDatabase.transaction.mockReturnValue(mockIDBTransaction);

      const searchPromise = documentLibrary.searchDocuments({ text: 'javascript' });

      // Simulate successful getAll
      if (mockGetAllRequest.onsuccess) {
        mockGetAllRequest.onsuccess({} as any);
      }

      const result = await searchPromise;

      expect(result.documents).toHaveLength(1);
      expect(result.documents[0].title).toBe('JavaScript Guide');
      expect(result.totalCount).toBe(1);
    });
  });
});

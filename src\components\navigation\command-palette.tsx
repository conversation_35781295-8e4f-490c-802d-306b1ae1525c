import React, { useState, useEffect, useMemo, useCallback } from 'react'
import { Dialog, DialogContent } from '@/components/ui/dialog'
import { Input } from '@/components/ui/input'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Badge } from '@/components/ui/badge'
import { cn } from '@/lib/utils'
import {
  Search,
  Edit3,
  Bookmark,
  Download,
  Settings,
  Layers,
  Users,
  Image,
  List,
  ZoomIn,
  ZoomOut,
  RotateCcw,
  Maximize,
  ArrowLeft,
  ArrowRight,
  Command
} from 'lucide-react'

export interface CommandAction {
  id: string
  label: string
  description?: string
  icon: React.ComponentType<{ className?: string }>
  action: () => void
  shortcut?: string
  category: string
  keywords?: string[]
  isRecent?: boolean
  frequency?: number
}

interface CommandPaletteProps {
  isOpen: boolean
  onClose: () => void
  actions: CommandAction[]
  placeholder?: string
  recentActions?: string[]
  onActionExecute?: (actionId: string) => void
}

const categoryIcons: Record<string, React.ComponentType<{ className?: string }>> = {
  navigation: ArrowRight,
  view: Search,
  annotation: Edit3,
  forms: Layers,
  tools: Settings,
  export: Download,
  bookmarks: Bookmark,
  collaboration: Users
}

const categoryLabels: Record<string, string> = {
  navigation: 'Navigation',
  view: 'View Controls',
  annotation: 'Annotations',
  forms: 'Forms',
  tools: 'Tools',
  export: 'Export',
  bookmarks: 'Bookmarks',
  collaboration: 'Collaboration'
}

export const CommandPalette: React.FC<CommandPaletteProps> = ({
  isOpen,
  onClose,
  actions,
  placeholder = "Type a command or search...",
  recentActions = [],
  onActionExecute
}) => {
  const [query, setQuery] = useState('')
  const [selectedIndex, setSelectedIndex] = useState(0)

  // Filter and sort actions based on query
  const filteredActions = useMemo(() => {
    if (!query.trim()) {
      // Show recent actions first, then all actions grouped by category
      const recent = actions.filter(action => recentActions.includes(action.id))
      const others = actions.filter(action => !recentActions.includes(action.id))
      return [...recent, ...others]
    }

    const normalizedQuery = query.toLowerCase().trim()
    
    return actions
      .filter(action => {
        const matchLabel = action.label.toLowerCase().includes(normalizedQuery)
        const matchDescription = action.description?.toLowerCase().includes(normalizedQuery)
        const matchKeywords = action.keywords?.some(keyword => 
          keyword.toLowerCase().includes(normalizedQuery)
        )
        const matchCategory = action.category.toLowerCase().includes(normalizedQuery)
        
        return matchLabel || matchDescription || matchKeywords || matchCategory
      })
      .sort((a, b) => {
        // Prioritize exact matches
        const aExact = a.label.toLowerCase() === normalizedQuery
        const bExact = b.label.toLowerCase() === normalizedQuery
        if (aExact && !bExact) return -1
        if (!aExact && bExact) return 1
        
        // Prioritize label matches over description matches
        const aLabelMatch = a.label.toLowerCase().includes(normalizedQuery)
        const bLabelMatch = b.label.toLowerCase().includes(normalizedQuery)
        if (aLabelMatch && !bLabelMatch) return -1
        if (!aLabelMatch && bLabelMatch) return 1
        
        // Sort by frequency/recent usage
        const aScore = (a.frequency || 0) + (recentActions.includes(a.id) ? 10 : 0)
        const bScore = (b.frequency || 0) + (recentActions.includes(b.id) ? 10 : 0)
        return bScore - aScore
      })
  }, [query, actions, recentActions])

  // Group actions by category for display
  const groupedActions = useMemo(() => {
    const groups: Record<string, CommandAction[]> = {}
    
    filteredActions.forEach(action => {
      if (!groups[action.category]) {
        groups[action.category] = []
      }
      groups[action.category].push(action)
    })
    
    return groups
  }, [filteredActions])

  // Reset selection when filtered actions change
  useEffect(() => {
    setSelectedIndex(0)
  }, [filteredActions])

  const executeAction = useCallback((action: CommandAction) => {
    action.action()
    onActionExecute?.(action.id)
    onClose()
  }, [onActionExecute, onClose])

  // Keyboard navigation
  useEffect(() => {
    if (!isOpen) return

    const handleKeyDown = (e: KeyboardEvent) => {
      switch (e.key) {
        case 'ArrowDown':
          e.preventDefault()
          setSelectedIndex(prev => 
            prev < filteredActions.length - 1 ? prev + 1 : 0
          )
          break
        case 'ArrowUp':
          e.preventDefault()
          setSelectedIndex(prev => 
            prev > 0 ? prev - 1 : filteredActions.length - 1
          )
          break
        case 'Enter':
          e.preventDefault()
          if (filteredActions[selectedIndex]) {
            executeAction(filteredActions[selectedIndex])
          }
          break
        case 'Escape':
          e.preventDefault()
          onClose()
          break
      }
    }

    document.addEventListener('keydown', handleKeyDown)
    return () => document.removeEventListener('keydown', handleKeyDown)
  }, [isOpen, filteredActions, selectedIndex, onClose, executeAction])



  const handleActionClick = useCallback((action: CommandAction) => {
    executeAction(action)
  }, [executeAction])

  // Reset state when dialog closes
  useEffect(() => {
    if (!isOpen) {
      setQuery('')
      setSelectedIndex(0)
    }
  }, [isOpen])

  let currentIndex = 0

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl p-0 gap-0">
        {/* Header */}
        <div className="flex items-center gap-3 p-4 border-b">
          <Command className="size-4 text-muted-foreground" />
          <Input
            placeholder={placeholder}
            value={query}
            onChange={(e) => setQuery(e.target.value)}
            className="border-0 focus-visible:ring-0 focus-visible:ring-offset-0 text-base"
            autoFocus
          />
          {query && (
            <Badge variant="secondary" className="text-xs">
              {filteredActions.length} results
            </Badge>
          )}
        </div>

        {/* Results */}
        <ScrollArea className="max-h-96">
          {filteredActions.length === 0 ? (
            <div className="p-8 text-center text-muted-foreground">
              <Search className="size-8 mx-auto mb-2 opacity-50" />
              <p>No commands found</p>
              <p className="text-sm mt-1">Try a different search term</p>
            </div>
          ) : (
            <div className="p-2">
              {!query && recentActions.length > 0 && (
                <div className="mb-4">
                  <div className="px-2 py-1 text-xs font-medium text-muted-foreground uppercase tracking-wide">
                    Recent
                  </div>
                  {actions
                    .filter(action => recentActions.includes(action.id))
                    .slice(0, 3)
                    .map(action => {
                      const isSelected = currentIndex === selectedIndex
                      currentIndex++
                      
                      return (
                        <button
                          key={action.id}
                          onClick={() => handleActionClick(action)}
                          className={cn(
                            "w-full flex items-center gap-3 px-3 py-2 rounded-md text-left transition-colors",
                            isSelected 
                              ? "bg-accent text-accent-foreground" 
                              : "hover:bg-accent/50"
                          )}
                        >
                          <action.icon className="size-4 shrink-0" />
                          <div className="flex-1 min-w-0">
                            <div className="font-medium truncate">{action.label}</div>
                            {action.description && (
                              <div className="text-xs text-muted-foreground truncate">
                                {action.description}
                              </div>
                            )}
                          </div>
                          {action.shortcut && (
                            <Badge variant="outline" className="text-xs shrink-0">
                              {action.shortcut}
                            </Badge>
                          )}
                        </button>
                      )
                    })}
                </div>
              )}

              {Object.entries(groupedActions).map(([category, categoryActions]) => {
                if (categoryActions.length === 0) return null
                
                const CategoryIcon = categoryIcons[category] || Settings
                
                return (
                  <div key={category} className="mb-4 last:mb-0">
                    <div className="flex items-center gap-2 px-2 py-1 text-xs font-medium text-muted-foreground uppercase tracking-wide">
                      <CategoryIcon className="size-3" />
                      {categoryLabels[category] || category}
                    </div>
                    
                    {categoryActions.map(action => {
                      const isSelected = currentIndex === selectedIndex
                      currentIndex++
                      
                      return (
                        <button
                          key={action.id}
                          onClick={() => handleActionClick(action)}
                          className={cn(
                            "w-full flex items-center gap-3 px-3 py-2 rounded-md text-left transition-colors",
                            isSelected 
                              ? "bg-accent text-accent-foreground" 
                              : "hover:bg-accent/50"
                          )}
                        >
                          <action.icon className="size-4 shrink-0" />
                          <div className="flex-1 min-w-0">
                            <div className="font-medium truncate">{action.label}</div>
                            {action.description && (
                              <div className="text-xs text-muted-foreground truncate">
                                {action.description}
                              </div>
                            )}
                          </div>
                          <div className="flex items-center gap-2 shrink-0">
                            {action.isRecent && (
                              <Badge variant="secondary" className="text-xs">
                                Recent
                              </Badge>
                            )}
                            {action.shortcut && (
                              <Badge variant="outline" className="text-xs">
                                {action.shortcut}
                              </Badge>
                            )}
                          </div>
                        </button>
                      )
                    })}
                  </div>
                )
              })}
            </div>
          )}
        </ScrollArea>

        {/* Footer */}
        <div className="flex items-center justify-between p-3 border-t text-xs text-muted-foreground">
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-1">
              <kbd className="px-1.5 py-0.5 bg-muted rounded text-xs">↑↓</kbd>
              <span>Navigate</span>
            </div>
            <div className="flex items-center gap-1">
              <kbd className="px-1.5 py-0.5 bg-muted rounded text-xs">↵</kbd>
              <span>Select</span>
            </div>
            <div className="flex items-center gap-1">
              <kbd className="px-1.5 py-0.5 bg-muted rounded text-xs">Esc</kbd>
              <span>Close</span>
            </div>
          </div>
          <div>
            {filteredActions.length > 0 && (
              <span>{selectedIndex + 1} of {filteredActions.length}</span>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}

// Helper function to create common command actions
export const createPDFCommands = (handlers: {
  // Navigation
  onGoToPage: (page: number) => void
  onNextPage: () => void
  onPrevPage: () => void
  onGoToFirstPage: () => void
  onGoToLastPage: () => void
  
  // View
  onZoomIn: () => void
  onZoomOut: () => void
  onResetZoom: () => void
  onRotate: () => void
  onToggleFullscreen: () => void
  
  // Tools
  onToggleSearch: () => void
  onAddBookmark: () => void
  onDownload: () => void
  onOpenSettings: () => void
  
  // Sidebar
  onOpenOutline: () => void
  onOpenThumbnails: () => void
  onOpenAnnotations: () => void
  onOpenForms: () => void
  
  // Current state
  currentPage?: number
  totalPages?: number
}): CommandAction[] => [
  // Navigation commands
  {
    id: 'next-page',
    label: 'Next Page',
    description: 'Go to the next page',
    icon: ArrowRight,
    action: handlers.onNextPage,
    shortcut: '→',
    category: 'navigation',
    keywords: ['page', 'forward', 'next']
  },
  {
    id: 'prev-page',
    label: 'Previous Page', 
    description: 'Go to the previous page',
    icon: ArrowLeft,
    action: handlers.onPrevPage,
    shortcut: '←',
    category: 'navigation',
    keywords: ['page', 'back', 'previous']
  },
  {
    id: 'first-page',
    label: 'First Page',
    description: 'Go to the first page',
    icon: ArrowLeft,
    action: handlers.onGoToFirstPage,
    shortcut: 'Home',
    category: 'navigation',
    keywords: ['first', 'beginning', 'start']
  },
  {
    id: 'last-page',
    label: 'Last Page',
    description: 'Go to the last page',
    icon: ArrowRight,
    action: handlers.onGoToLastPage,
    shortcut: 'End',
    category: 'navigation',
    keywords: ['last', 'end', 'final']
  },
  
  // View commands
  {
    id: 'zoom-in',
    label: 'Zoom In',
    description: 'Increase zoom level',
    icon: ZoomIn,
    action: handlers.onZoomIn,
    shortcut: '+',
    category: 'view',
    keywords: ['zoom', 'magnify', 'larger']
  },
  {
    id: 'zoom-out',
    label: 'Zoom Out',
    description: 'Decrease zoom level',
    icon: ZoomOut,
    action: handlers.onZoomOut,
    shortcut: '-',
    category: 'view',
    keywords: ['zoom', 'shrink', 'smaller']
  },
  {
    id: 'reset-zoom',
    label: 'Reset Zoom',
    description: 'Reset zoom to 100%',
    icon: ZoomIn,
    action: handlers.onResetZoom,
    shortcut: '0',
    category: 'view',
    keywords: ['zoom', 'reset', 'original', '100%']
  },
  {
    id: 'rotate',
    label: 'Rotate',
    description: 'Rotate page 90 degrees',
    icon: RotateCcw,
    action: handlers.onRotate,
    shortcut: 'R',
    category: 'view',
    keywords: ['rotate', 'turn', 'orientation']
  },
  {
    id: 'fullscreen',
    label: 'Toggle Fullscreen',
    description: 'Enter or exit fullscreen mode',
    icon: Maximize,
    action: handlers.onToggleFullscreen,
    shortcut: 'F11',
    category: 'view',
    keywords: ['fullscreen', 'full', 'screen', 'immersive']
  },
  
  // Tool commands
  {
    id: 'search',
    label: 'Search Document',
    description: 'Search for text in the document',
    icon: Search,
    action: handlers.onToggleSearch,
    shortcut: 'Ctrl+F',
    category: 'tools',
    keywords: ['search', 'find', 'text', 'lookup']
  },
  {
    id: 'bookmark',
    label: 'Add Bookmark',
    description: 'Bookmark the current page',
    icon: Bookmark,
    action: handlers.onAddBookmark,
    shortcut: 'Ctrl+D',
    category: 'bookmarks',
    keywords: ['bookmark', 'save', 'mark', 'favorite']
  },
  {
    id: 'download',
    label: 'Download PDF',
    description: 'Download the PDF file',
    icon: Download,
    action: handlers.onDownload,
    shortcut: 'Ctrl+S',
    category: 'export',
    keywords: ['download', 'save', 'export', 'file']
  },
  
  // Sidebar commands
  {
    id: 'outline',
    label: 'Document Outline',
    description: 'Show document outline and table of contents',
    icon: List,
    action: handlers.onOpenOutline,
    shortcut: 'Ctrl+1',
    category: 'navigation',
    keywords: ['outline', 'toc', 'table', 'contents', 'structure']
  },
  {
    id: 'thumbnails',
    label: 'Page Thumbnails',
    description: 'Show page thumbnails for quick navigation',
    icon: Image,
    action: handlers.onOpenThumbnails,
    shortcut: 'Ctrl+2',
    category: 'navigation',
    keywords: ['thumbnails', 'pages', 'preview', 'overview']
  },
  {
    id: 'annotations',
    label: 'Annotations',
    description: 'View and manage annotations',
    icon: Edit3,
    action: handlers.onOpenAnnotations,
    shortcut: 'Ctrl+3',
    category: 'annotation',
    keywords: ['annotations', 'notes', 'highlights', 'comments']
  },
  {
    id: 'forms',
    label: 'Forms',
    description: 'View and fill PDF forms',
    icon: Layers,
    action: handlers.onOpenForms,
    shortcut: 'Ctrl+4',
    category: 'forms',
    keywords: ['forms', 'fields', 'fill', 'input']
  }
]

export default CommandPalette
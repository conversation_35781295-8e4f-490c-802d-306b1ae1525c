"use client";

import { useState, useCallback, useEffect, useRef } from 'react';
import { toast } from 'sonner';
import useDocumentManager from '../document-manager';
import type { DocumentInstance } from '@/lib/types/pdf';
import { EnhancedErrorHandler } from '../enhanced-error-handler';

interface MultiDocumentStateOptions {
  maxDocuments?: number;
  autoSave?: boolean;
  persistState?: boolean;
  onDocumentChange?: (document: DocumentInstance | null) => void;
  onError?: (error: Error, documentId?: string) => void;
}

interface DocumentOperations {
  openDocument: (file: string | File) => Promise<string>;
  closeDocument: (documentId: string) => void;
  switchToDocument: (documentId: string) => void;
  reloadDocument: (documentId: string) => Promise<void>;
  updateDocumentState: (documentId: string, updates: Partial<DocumentInstance>) => void;
  getDocumentState: (documentId: string) => DocumentInstance | undefined;
  getCurrentDocument: () => DocumentInstance | undefined;
  getAllDocuments: () => DocumentInstance[];
  closeAllDocuments: () => void;
}

interface DocumentLoadingState {
  isLoading: boolean;
  loadingDocuments: Set<string>;
  loadingProgress: Map<string, number>;
  loadingStage: Map<string, 'downloading' | 'parsing' | 'rendering' | 'complete'>;
}

export function useMultiDocumentState(options: MultiDocumentStateOptions = {}) {
  const {
    maxDocuments = 5,
    autoSave = true,
    persistState = true,
    onDocumentChange,
    onError
  } = options;

  // Document manager state
  const [managerState, managerActions] = useDocumentManager({
    maxDocuments,
    onActiveDocumentChange: (documentId) => {
      const document = documentId ? (managerActions.getDocument(documentId) || null) : null;
      onDocumentChange?.(document);
    },
    onDocumentClose: (documentId) => {
      if (autoSave) {
        saveDocumentState(documentId);
      }
    }
  });

  // Loading state management
  const [loadingState, setLoadingState] = useState<DocumentLoadingState>({
    isLoading: false,
    loadingDocuments: new Set(),
    loadingProgress: new Map(),
    loadingStage: new Map()
  });

  // Error tracking
  const [documentErrors, setDocumentErrors] = useState<Map<string, Error>>(new Map());
  const retryCountRef = useRef<Map<string, number>>(new Map());

  // Persist state to localStorage
  const saveDocumentState = useCallback((documentId: string) => {
    if (!persistState) return;
    
    const document = managerActions.getDocument(documentId);
    if (!document) return;

    try {
      const stateToSave = {
        pageNumber: document.pageNumber,
        scale: document.scale,
        rotation: document.rotation,
        bookmarks: document.bookmarks,
        annotations: document.annotations,
        formData: document.formData,
        lastAccessed: document.lastAccessed
      };
      
      localStorage.setItem(`pdf-document-${documentId}`, JSON.stringify(stateToSave));
    } catch (error) {
      console.warn('Failed to save document state:', error);
    }
  }, [persistState, managerActions]);

  // Load state from localStorage
  const loadDocumentState = useCallback((documentId: string) => {
    if (!persistState) return;

    try {
      const savedState = localStorage.getItem(`pdf-document-${documentId}`);
      if (savedState) {
        const state = JSON.parse(savedState);
        managerActions.updateDocument(documentId, state);
      }
    } catch (error) {
      console.warn('Failed to load document state:', error);
    }
  }, [persistState, managerActions]);

  // Update loading state for a specific document
  const updateLoadingState = useCallback((
    documentId: string, 
    updates: {
      isLoading?: boolean;
      progress?: number;
      stage?: 'downloading' | 'parsing' | 'rendering' | 'complete';
    }
  ) => {
    setLoadingState(prev => {
      const newLoadingDocuments = new Set(prev.loadingDocuments);
      const newProgress = new Map(prev.loadingProgress);
      const newStage = new Map(prev.loadingStage);

      if (updates.isLoading !== undefined) {
        if (updates.isLoading) {
          newLoadingDocuments.add(documentId);
        } else {
          newLoadingDocuments.delete(documentId);
          newProgress.delete(documentId);
          newStage.delete(documentId);
        }
      }

      if (updates.progress !== undefined) {
        newProgress.set(documentId, updates.progress);
      }

      if (updates.stage !== undefined) {
        newStage.set(documentId, updates.stage);
      }

      return {
        isLoading: newLoadingDocuments.size > 0,
        loadingDocuments: newLoadingDocuments,
        loadingProgress: newProgress,
        loadingStage: newStage
      };
    });
  }, []);

  // Handle document loading with progress tracking
  const openDocument = useCallback(async (file: string | File): Promise<string> => {
    try {
      const documentId = await managerActions.openDocument(file);
      
      // Start loading state
      updateLoadingState(documentId, { 
        isLoading: true, 
        progress: 0, 
        stage: 'downloading' 
      });

      // Simulate progress updates (in real implementation, this would come from PDF.js)
      const progressInterval = setInterval(() => {
        updateLoadingState(documentId, { 
          progress: Math.min(90, (loadingState.loadingProgress.get(documentId) || 0) + 10)
        });
      }, 200);

      // Clear any previous errors
      setDocumentErrors(prev => {
        const newErrors = new Map(prev);
        newErrors.delete(documentId);
        return newErrors;
      });
      retryCountRef.current.delete(documentId);

      // Complete loading after a delay (this would be replaced by actual PDF loading)
      setTimeout(() => {
        clearInterval(progressInterval);
        updateLoadingState(documentId, { 
          isLoading: false, 
          progress: 100, 
          stage: 'complete' 
        });
        
        // Load saved state if available
        loadDocumentState(documentId);
      }, 1000);

      return documentId;
    } catch (error) {
      const documentId = 'temp-' + Date.now();
      handleDocumentError(error as Error, documentId);
      throw error;
    }
  }, [managerActions, updateLoadingState, loadDocumentState, loadingState.loadingProgress]);

  // Handle document errors with enhanced error handling
  const handleDocumentError = useCallback((error: Error, documentId: string) => {
    const categorizedError = EnhancedErrorHandler.categorizeAndHandle(error, 'document loading', {
      showToast: true,
      onRetry: () => reloadDocument(documentId)
    });

    setDocumentErrors(prev => new Map(prev).set(documentId, error));
    
    // Update document with error state
    managerActions.updateDocument(documentId, {
      hasError: true,
      errorMessage: categorizedError.message,
      isLoading: false
    });

    updateLoadingState(documentId, { isLoading: false });
    onError?.(error, documentId);
  }, [managerActions, updateLoadingState, onError]);

  // Reload a failed document
  const reloadDocument = useCallback(async (documentId: string): Promise<void> => {
    const document = managerActions.getDocument(documentId);
    if (!document) return;

    const retryCount = retryCountRef.current.get(documentId) || 0;
    retryCountRef.current.set(documentId, retryCount + 1);

    try {
      // Clear error state
      managerActions.updateDocument(documentId, {
        hasError: false,
        errorMessage: undefined,
        isLoading: true
      });

      setDocumentErrors(prev => {
        const newErrors = new Map(prev);
        newErrors.delete(documentId);
        return newErrors;
      });

      updateLoadingState(documentId, { 
        isLoading: true, 
        progress: 0, 
        stage: 'downloading' 
      });

      // Simulate reload process
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      updateLoadingState(documentId, { 
        isLoading: false, 
        stage: 'complete' 
      });

      managerActions.updateDocument(documentId, {
        isLoading: false,
        lastAccessed: Date.now()
      });

      toast.success(`Document reloaded: ${document.title}`);
    } catch (error) {
      handleDocumentError(error as Error, documentId);
    }
  }, [managerActions, updateLoadingState, handleDocumentError]);

  // Auto-save document state when it changes
  useEffect(() => {
    if (!autoSave) return;

    const activeDocument = managerActions.getActiveDocument();
    if (activeDocument) {
      const timeoutId = setTimeout(() => {
        saveDocumentState(activeDocument.id);
      }, 1000); // Debounce saves

      return () => clearTimeout(timeoutId);
    }
  }, [managerActions, autoSave, saveDocumentState]);

  // Document operations interface
  const operations: DocumentOperations = {
    openDocument,
    closeDocument: managerActions.closeDocument,
    switchToDocument: managerActions.setActiveDocument,
    reloadDocument,
    updateDocumentState: managerActions.updateDocument,
    getDocumentState: managerActions.getDocument,
    getCurrentDocument: managerActions.getActiveDocument,
    getAllDocuments: managerActions.getAllDocuments,
    closeAllDocuments: managerActions.clearAllDocuments
  };

  return {
    // State
    documents: managerActions.getAllDocuments(),
    activeDocument: managerActions.getActiveDocument(),
    activeDocumentId: managerState.activeDocumentId,
    loadingState,
    documentErrors,
    
    // Operations
    ...operations,
    
    // Utilities
    isDocumentLoading: (documentId: string) => loadingState.loadingDocuments.has(documentId),
    getDocumentProgress: (documentId: string) => loadingState.loadingProgress.get(documentId) || 0,
    getDocumentStage: (documentId: string) => loadingState.loadingStage.get(documentId),
    hasDocumentError: (documentId: string) => documentErrors.has(documentId),
    getDocumentError: (documentId: string) => documentErrors.get(documentId),
    
    // State management
    saveDocumentState,
    loadDocumentState
  };
}

export default useMultiDocumentState;

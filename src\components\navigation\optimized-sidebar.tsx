"use client";

import React, { useState, useCallback, useEffect, useRef } from 'react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import {
  X,
  ChevronLeft,
  ChevronRight,
  Pin,
  PinOff,
  Search,
  Bookmark,
  FileText,
  Grid3X3,
  PenTool,
  Settings,
  List,
  Eye,
  EyeOff,
  Minimize2,
  Maximize2,
  MoreHorizontal
} from 'lucide-react';
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from '@/components/ui/tabs';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { cn } from '@/lib/utils';

export type SidebarMode = 'full' | 'compact' | 'mini' | 'hidden';
export type SidebarPosition = 'left' | 'right';

interface SidebarTab {
  id: string;
  label: string;
  icon: React.ComponentType<{ className?: string }>;
  content: React.ReactNode;
  badge?: string | number;
  priority?: 'high' | 'medium' | 'low'; // For compact mode ordering
}

interface OptimizedSidebarProps {
  isOpen: boolean;
  onClose: () => void;
  mode: SidebarMode;
  onModeChange: (mode: SidebarMode) => void;
  position?: SidebarPosition;
  tabs: SidebarTab[];
  activeTab: string;
  onTabChange: (tabId: string) => void;
  isPinned?: boolean;
  onPinnedChange?: (pinned: boolean) => void;
  autoHide?: boolean;
  autoHideDelay?: number;
  className?: string;
}

export default function OptimizedSidebar({
  isOpen,
  onClose,
  mode,
  onModeChange,
  position = 'left',
  tabs,
  activeTab,
  onTabChange,
  isPinned = false,
  onPinnedChange,
  autoHide = false,
  autoHideDelay = 3000,
  className
}: OptimizedSidebarProps) {
  const [isHovered, setIsHovered] = useState(false);
  const [isCollapsed, setIsCollapsed] = useState(mode === 'compact' || mode === 'mini');
  const autoHideTimeoutRef = useRef<NodeJS.Timeout>();
  const sidebarRef = useRef<HTMLDivElement>(null);

  // Auto-hide functionality
  useEffect(() => {
    if (!autoHide || isPinned || !isOpen) return;

    const resetTimeout = () => {
      if (autoHideTimeoutRef.current) {
        clearTimeout(autoHideTimeoutRef.current);
      }
      autoHideTimeoutRef.current = setTimeout(() => {
        if (!isHovered) {
          onClose();
        }
      }, autoHideDelay);
    };

    resetTimeout();

    return () => {
      if (autoHideTimeoutRef.current) {
        clearTimeout(autoHideTimeoutRef.current);
      }
    };
  }, [autoHide, isPinned, isOpen, isHovered, autoHideDelay, onClose]);

  // Handle mode changes
  const handleModeChange = useCallback((newMode: SidebarMode) => {
    onModeChange(newMode);
    setIsCollapsed(newMode === 'compact' || newMode === 'mini');
  }, [onModeChange]);

  // Toggle collapsed state
  const toggleCollapsed = useCallback(() => {
    const newCollapsed = !isCollapsed;
    setIsCollapsed(newCollapsed);
    
    if (newCollapsed) {
      handleModeChange(mode === 'full' ? 'compact' : 'mini');
    } else {
      handleModeChange('full');
    }
  }, [isCollapsed, mode, handleModeChange]);

  // Get sidebar width based on mode
  const getSidebarWidth = () => {
    switch (mode) {
      case 'full':
        return 'w-80';
      case 'compact':
        return 'w-64';
      case 'mini':
        return 'w-12';
      case 'hidden':
        return 'w-0';
      default:
        return 'w-80';
    }
  };

  // Filter and sort tabs based on mode
  const getVisibleTabs = () => {
    if (mode === 'mini') {
      // Show only high priority tabs in mini mode
      return tabs.filter(tab => tab.priority === 'high').slice(0, 4);
    }
    if (mode === 'compact') {
      // Show high and medium priority tabs
      return tabs.filter(tab => tab.priority !== 'low').slice(0, 6);
    }
    return tabs;
  };

  // Render tab trigger
  const renderTabTrigger = (tab: SidebarTab) => {
    const Icon = tab.icon;
    
    if (mode === 'mini') {
      return (
        <Tooltip key={tab.id}>
          <TooltipTrigger asChild>
            <Button
              variant={activeTab === tab.id ? "default" : "ghost"}
              size="sm"
              onClick={() => onTabChange(tab.id)}
              className="w-8 h-8 p-0 relative"
            >
              <Icon className="h-4 w-4" />
              {tab.badge && (
                <span className="absolute -top-1 -right-1 bg-primary text-primary-foreground text-xs rounded-full w-4 h-4 flex items-center justify-center">
                  {tab.badge}
                </span>
              )}
            </Button>
          </TooltipTrigger>
          <TooltipContent side={position === 'left' ? 'right' : 'left'}>
            {tab.label}
          </TooltipContent>
        </Tooltip>
      );
    }

    return (
      <TabsTrigger
        key={tab.id}
        value={tab.id}
        className={cn(
          "flex items-center gap-2 justify-start w-full",
          mode === 'compact' && "text-xs px-2"
        )}
      >
        <Icon className={cn("h-4 w-4", mode === 'compact' && "h-3 w-3")} />
        {mode !== 'mini' && (
          <>
            <span className={cn("truncate", mode === 'compact' && "text-xs")}>
              {tab.label}
            </span>
            {tab.badge && (
              <span className="bg-primary text-primary-foreground text-xs rounded-full px-1.5 py-0.5 min-w-[1.25rem] text-center">
                {tab.badge}
              </span>
            )}
          </>
        )}
      </TabsTrigger>
    );
  };

  // Render sidebar header
  const renderHeader = () => (
    <div className={cn(
      "flex items-center justify-between border-b",
      mode === 'mini' ? "p-1" : "p-3"
    )}>
      {mode !== 'mini' && (
        <h2 className={cn(
          "font-semibold",
          mode === 'compact' ? "text-sm" : "text-base"
        )}>
          Tools
        </h2>
      )}
      
      <div className="flex items-center gap-1">
        {/* Pin/Unpin button */}
        {onPinnedChange && mode !== 'mini' && (
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => onPinnedChange(!isPinned)}
                className="h-6 w-6 p-0"
              >
                {isPinned ? (
                  <PinOff className="h-3 w-3" />
                ) : (
                  <Pin className="h-3 w-3" />
                )}
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              {isPinned ? 'Unpin sidebar' : 'Pin sidebar'}
            </TooltipContent>
          </Tooltip>
        )}

        {/* Collapse/Expand button */}
        <Tooltip>
          <TooltipTrigger asChild>
            <Button
              variant="ghost"
              size="sm"
              onClick={toggleCollapsed}
              className="h-6 w-6 p-0"
            >
              {isCollapsed ? (
                position === 'left' ? (
                  <ChevronRight className="h-3 w-3" />
                ) : (
                  <ChevronLeft className="h-3 w-3" />
                )
              ) : (
                position === 'left' ? (
                  <ChevronLeft className="h-3 w-3" />
                ) : (
                  <ChevronRight className="h-3 w-3" />
                )
              )}
            </Button>
          </TooltipTrigger>
          <TooltipContent>
            {isCollapsed ? 'Expand sidebar' : 'Collapse sidebar'}
          </TooltipContent>
        </Tooltip>

        {/* Close button */}
        <Tooltip>
          <TooltipTrigger asChild>
            <Button
              variant="ghost"
              size="sm"
              onClick={onClose}
              className="h-6 w-6 p-0"
            >
              <X className="h-3 w-3" />
            </Button>
          </TooltipTrigger>
          <TooltipContent>Close sidebar</TooltipContent>
        </Tooltip>
      </div>
    </div>
  );

  if (mode === 'hidden' || !isOpen) {
    return null;
  }

  const visibleTabs = getVisibleTabs();

  return (
    <TooltipProvider>
      {/* Backdrop for mobile */}
      {isOpen && (
        <div
          className="fixed inset-0 bg-black/20 backdrop-blur-sm z-40 lg:hidden"
          onClick={onClose}
        />
      )}

      {/* Sidebar */}
      <div
        ref={sidebarRef}
        className={cn(
          "fixed top-0 h-full bg-background border-r shadow-lg z-50 transition-all duration-300 ease-in-out flex flex-col",
          getSidebarWidth(),
          position === 'left' ? 'left-0' : 'right-0',
          isOpen ? 'translate-x-0' : (position === 'left' ? '-translate-x-full' : 'translate-x-full'),
          className
        )}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
      >
        {/* Header */}
        {renderHeader()}

        {/* Content */}
        <div className="flex-1 min-h-0">
          {mode === 'mini' ? (
            // Mini mode - vertical icon stack
            <div className="flex flex-col gap-1 p-1">
              {visibleTabs.map(renderTabTrigger)}
            </div>
          ) : (
            // Full/Compact mode - tabbed interface
            <Tabs value={activeTab} onValueChange={onTabChange} className="h-full flex flex-col">
              <TabsList className={cn(
                "grid w-full",
                mode === 'compact' ? "h-8" : "h-10"
              )} style={{ gridTemplateColumns: `repeat(${Math.min(visibleTabs.length, 4)}, 1fr)` }}>
                {visibleTabs.slice(0, 4).map(renderTabTrigger)}
              </TabsList>

              {/* Tab content */}
              <div className="flex-1 min-h-0">
                {visibleTabs.map(tab => (
                  <TabsContent
                    key={tab.id}
                    value={tab.id}
                    className="h-full m-0 data-[state=active]:flex data-[state=active]:flex-col"
                  >
                    <ScrollArea className="flex-1">
                      <div className={cn(
                        mode === 'compact' ? "p-2" : "p-3"
                      )}>
                        {tab.content}
                      </div>
                    </ScrollArea>
                  </TabsContent>
                ))}
              </div>
            </Tabs>
          )}
        </div>

        {/* Footer with mode selector */}
        {mode !== 'mini' && (
          <div className="border-t p-2">
            <div className="flex items-center justify-between">
              <span className="text-xs text-muted-foreground">
                {mode === 'compact' ? 'Compact' : 'Full'} View
              </span>
              <div className="flex gap-1">
                <Button
                  variant={mode === 'full' ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => handleModeChange('full')}
                  className="h-6 w-6 p-0"
                  title="Full view"
                >
                  <Maximize2 className="h-3 w-3" />
                </Button>
                <Button
                  variant={mode === 'compact' ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => handleModeChange('compact')}
                  className="h-6 w-6 p-0"
                  title="Compact view"
                >
                  <Minimize2 className="h-3 w-3" />
                </Button>
                <Button
                  variant={mode === 'mini' ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => handleModeChange('mini')}
                  className="h-6 w-6 p-0"
                  title="Mini view"
                >
                  <MoreHorizontal className="h-3 w-3" />
                </Button>
              </div>
            </div>
          </div>
        )}
      </div>
    </TooltipProvider>
  );
}

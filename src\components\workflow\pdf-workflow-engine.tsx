"use client"

import { useState, use<PERSON><PERSON>back, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Progress } from "@/components/ui/progress"
import { Play, Square, Clock, CheckCircle, XCircle, AlertCircle, Users, Mail, RefreshCw, Eye } from "lucide-react"
import { toast } from "sonner"
import type { Workflow, WorkflowNode } from "./pdf-workflow-builder"
import type { FormData } from "../forms/pdf-form-manager"

export type WorkflowExecutionStatus = "pending" | "running" | "waiting_approval" | "completed" | "failed" | "cancelled"

export interface WorkflowExecution {
  id: string
  workflowId: string
  workflowName: string
  status: WorkflowExecutionStatus
  currentNodeId: string | null
  formData: FormData
  context: Record<string, unknown>
  startedAt: number
  completedAt?: number
  error?: string
  steps: WorkflowStep[]
  approvals: WorkflowApproval[]
  notifications: WorkflowNotification[]
}

export interface WorkflowStep {
  id: string
  nodeId: string
  nodeType: string
  status: "pending" | "running" | "completed" | "failed" | "skipped"
  startedAt?: number
  completedAt?: number
  input?: unknown
  output?: unknown
  error?: string
  duration?: number
}

export interface WorkflowApproval {
  id: string
  nodeId: string
  approver: string
  status: "pending" | "approved" | "rejected"
  comment?: string
  timestamp?: number
  expiresAt?: number
}

export interface WorkflowNotification {
  id: string
  nodeId: string
  type: "email" | "webhook" | "system"
  recipient: string
  subject?: string
  message: string
  status: "pending" | "sent" | "failed"
  timestamp: number
  error?: string
}

interface PDFWorkflowEngineProps {
  workflows: Workflow[]
  executions: WorkflowExecution[]
  onExecutionsChange: (executions: WorkflowExecution[]) => void
  formData: FormData
  currentUser: string
}

export default function PDFWorkflowEngine({
  workflows,
  executions,
  onExecutionsChange,
  formData,
  currentUser,
}: PDFWorkflowEngineProps) {
  const [selectedExecution, setSelectedExecution] = useState<WorkflowExecution | null>(null)
  const [isExecuting, setIsExecuting] = useState(false)

  // Execute individual node
  const executeNode = useCallback(async (
    node: WorkflowNode,
    execution: WorkflowExecution,
    workflow: Workflow,
  ): Promise<{
    step: WorkflowStep
    executionStatus: WorkflowExecutionStatus
    nextNodeId: string | null
    approvals: WorkflowApproval[]
    notifications: WorkflowNotification[]
  }> => {
    const step: WorkflowStep = {
      id: `step-${Date.now()}`,
      nodeId: node.id,
      nodeType: node.type,
      status: "running",
      startedAt: Date.now(),
    }

    const approvals: WorkflowApproval[] = []
    const notifications: WorkflowNotification[] = []

    try {
      let nextNodeId: string | null = null

      switch (node.type) {
        case "start":
          step.status = "completed"
          nextNodeId = workflow.connections.find((c) => c.sourceId === node.id)?.targetId || null
          break

        case "approval":
          if (node.config.approvers && node.config.approvers.length > 0) {
            // Create individual approval records for each approver
            for (const approver of node.config.approvers) {
              const approval: WorkflowApproval = {
                id: `approval-${Date.now()}-${approver}`,
                nodeId: node.id,
                approver: approver,
                status: "pending",
                timestamp: Date.now(),
                expiresAt: node.config.timeoutDays ? Date.now() + (node.config.timeoutDays * 24 * 60 * 60 * 1000) : undefined,
              }
              approvals.push(approval)
            }
            step.status = "completed"
            nextNodeId = workflow.connections.find((c) => c.sourceId === node.id)?.targetId || null
          }
          break

        case "condition":
          if (node.config.conditions) {
            const conditionMet = evaluateConditions(node.config.conditions, execution.formData)
            step.status = "completed"
            step.output = { conditionMet }

            const connection = workflow.connections.find((c) =>
              c.sourceId === node.id &&
              (conditionMet ? c.condition === "true" : c.condition === "false")
            )
            nextNodeId = connection?.targetId || null
          }
          break

        case "notification":
          if (node.config.recipients) {
            // Create individual notification records for each recipient
            for (const recipient of node.config.recipients) {
              const notification: WorkflowNotification = {
                id: `notification-${Date.now()}-${recipient}`,
                nodeId: node.id,
                type: "email",
                recipient: recipient,
                subject: node.config.subject || "Workflow Notification",
                message: node.config.message || "A workflow step has been completed",
                status: "sent",
                timestamp: Date.now(),
              }
              notifications.push(notification)
            }
          }
          step.status = "completed"
          nextNodeId = workflow.connections.find((c) => c.sourceId === node.id)?.targetId || null
          break

        case "webhook":
          // Simulate webhook call
          step.status = "completed"
          step.output = { webhookResponse: "success" }
          nextNodeId = workflow.connections.find((c) => c.sourceId === node.id)?.targetId || null
          break

        case "delay":
          // Simulate delay
          step.status = "completed"
          nextNodeId = workflow.connections.find((c) => c.sourceId === node.id)?.targetId || null
          break

        case "end":
          step.status = "completed"
          break

        default:
          step.status = "completed"
          nextNodeId = workflow.connections.find((c) => c.sourceId === node.id)?.targetId || null
      }

      step.completedAt = Date.now()
      step.duration = step.startedAt ? step.completedAt - step.startedAt : 0

      return {
        step,
        executionStatus: "running",
        nextNodeId,
        approvals,
        notifications,
      }
    } catch (error) {
      step.status = "failed"
      step.error = error instanceof Error ? error.message : "Unknown error"
      step.completedAt = Date.now()

      return {
        step,
        executionStatus: "failed",
        nextNodeId: null,
        approvals,
        notifications,
      }
    }
  }, [])

  // Execute workflow
  const executeWorkflow = useCallback(
    async (execution: WorkflowExecution, workflow: Workflow) => {
      setIsExecuting(true)

      try {
        const currentExecution = { ...execution }
        let currentNodeId = execution.currentNodeId

        while (currentNodeId) {
          const currentNode = workflow.nodes.find((n) => n.id === currentNodeId)
          if (!currentNode) break

          const result = await executeNode(currentNode, currentExecution, workflow)

          currentExecution.steps.push(result.step)
          currentExecution.approvals.push(...result.approvals)
          currentExecution.notifications.push(...result.notifications)

          if (result.executionStatus === "failed") {
            currentExecution.status = "failed"
            break
          }

          if (result.nextNodeId) {
            currentNodeId = result.nextNodeId
            currentExecution.currentNodeId = currentNodeId
          } else {
            currentExecution.status = "completed"
            currentExecution.completedAt = Date.now()
            break
          }
        }

        const updatedExecutions = executions.map((e) =>
          e.id === currentExecution.id ? currentExecution : e
        )
        onExecutionsChange(updatedExecutions)
        setSelectedExecution(currentExecution)

        toast.success("Workflow completed", {
          description: `Workflow "${workflow.name}" has been completed`,
        })
      } catch (error) {
        console.error("Workflow execution failed:", error)

        toast.error("Workflow failed", {
          description: "An error occurred during workflow execution",
        })
      } finally {
        setIsExecuting(false)
      }
    },
    [executions, onExecutionsChange, executeNode],
  )

  // Start workflow execution
  const startWorkflow = useCallback(
    async (workflowId: string, triggerData: FormData) => {
      const workflow = workflows.find((w) => w.id === workflowId)
      if (!workflow) {
        toast.error("Workflow not found", {
          description: "The selected workflow could not be found",
        })
        return
      }

      if (workflow.status !== "active") {
        toast.error("Workflow inactive", {
          description: "Only active workflows can be executed",
        })
        return
      }

      const startNode = workflow.nodes.find((n) => n.type === "start")
      if (!startNode) {
        toast.error("Invalid workflow", {
          description: "Workflow must have a start node",
        })
        return
      }

      const execution: WorkflowExecution = {
        id: `exec-${Date.now()}`,
        workflowId: workflow.id,
        workflowName: workflow.name,
        status: "running",
        currentNodeId: startNode.id,
        formData: triggerData,
        context: {
          startedBy: currentUser,
          triggerType: "form_submit",
        },
        startedAt: Date.now(),
        steps: [],
        approvals: [],
        notifications: [],
      }

      const updatedExecutions = [...executions, execution]
      onExecutionsChange(updatedExecutions)
      setSelectedExecution(execution)

      // Start execution
      await executeWorkflow(execution, workflow)

      toast.success("Workflow started", {
        description: `Workflow "${workflow.name}" has been started`,
      })
    },
    [workflows, executions, onExecutionsChange, currentUser, executeWorkflow],
  )








  // Evaluate conditions
  const evaluateConditions = (conditions: Array<{field: string; operator: string; value: unknown}>, formData: FormData): boolean => {
    if (conditions.length === 0) return true

    return conditions.every((condition) => {
      const fieldValue = formData[condition.field]
      const conditionValue = condition.value

      switch (condition.operator) {
        case "equals":
          return fieldValue === conditionValue
        case "not_equals":
          return fieldValue !== conditionValue
        case "contains":
          return String(fieldValue).includes(String(conditionValue))
        case "greater_than":
          return Number(fieldValue) > Number(conditionValue)
        case "less_than":
          return Number(fieldValue) < Number(conditionValue)
        default:
          return false
      }
    })
  }

  // Handle approval
  const handleApproval = (approvalId: string, decision: "approved" | "rejected", comment?: string) => {
    const updatedExecutions = executions.map((execution) => {
      const approval = execution.approvals.find((a) => a.id === approvalId)
      if (!approval) return execution

      const updatedApprovals = execution.approvals.map((a) =>
        a.id === approvalId ? { ...a, status: decision, comment, timestamp: Date.now() } : a,
      )

      // Check if all required approvals are complete
      const nodeApprovals = updatedApprovals.filter((a) => a.nodeId === approval.nodeId)
      const workflow = workflows.find((w) => w.id === execution.workflowId)
      const node = workflow?.nodes.find((n) => n.id === approval.nodeId)

      let shouldContinue = false
      if (node?.config.approvalType === "any") {
        shouldContinue = nodeApprovals.some((a) => a.status === "approved")
      } else if (node?.config.approvalType === "all") {
        shouldContinue = nodeApprovals.every((a) => a.status === "approved")
      } else if (node?.config.approvalType === "majority") {
        const approvedCount = nodeApprovals.filter((a) => a.status === "approved").length
        shouldContinue = approvedCount > nodeApprovals.length / 2
      }

      const hasRejection = nodeApprovals.some((a) => a.status === "rejected")

      let newStatus = execution.status
      let nextNodeId = execution.currentNodeId

      if (hasRejection) {
        newStatus = "failed"
        nextNodeId = null
      } else if (shouldContinue) {
        newStatus = "running"
        // Find next node
        const connection = workflow?.connections.find(
          (c) => c.sourceId === approval.nodeId && c.condition === "approved",
        )
        nextNodeId = connection?.targetId || null
      }

      return {
        ...execution,
        approvals: updatedApprovals,
        status: newStatus,
        currentNodeId: nextNodeId,
      }
    })

    onExecutionsChange(updatedExecutions)

    toast.success(`Approval ${decision}`, {
      description: `You have ${decision} the workflow step`,
    })
  }

  // Cancel execution
  const cancelExecution = (executionId: string) => {
    const updatedExecutions = executions.map((execution) =>
      execution.id === executionId
        ? { ...execution, status: "cancelled" as const, completedAt: Date.now() }
        : execution,
    )
    onExecutionsChange(updatedExecutions)

    toast.success("Workflow cancelled", {
      description: "The workflow execution has been cancelled",
    })
  }

  // Auto-trigger workflows on form submit
  useEffect(() => {
    const activeWorkflows = workflows.filter((w) => w.status === "active" && w.triggers.formSubmit)

    if (activeWorkflows.length > 0 && Object.keys(formData).length > 0) {
      // Check if we should trigger any workflows
      activeWorkflows.forEach((workflow) => {
        // Simple trigger logic - in a real app, you'd have more sophisticated triggering
        const hasRecentExecution = executions.some(
          (e) => e.workflowId === workflow.id && Date.now() - e.startedAt < 60000, // Within last minute
        )

        if (!hasRecentExecution) {
          startWorkflow(workflow.id, formData)
        }
      })
    }
  }, [formData, workflows, executions, startWorkflow])

  const getStatusIcon = (status: WorkflowExecutionStatus) => {
    switch (status) {
      case "running":
        return <RefreshCw className="h-4 w-4 animate-spin text-blue-500" />
      case "completed":
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case "failed":
        return <XCircle className="h-4 w-4 text-red-500" />
      case "waiting_approval":
        return <Clock className="h-4 w-4 text-yellow-500" />
      case "cancelled":
        return <Square className="h-4 w-4 text-gray-500" />
      default:
        return <AlertCircle className="h-4 w-4 text-gray-500" />
    }
  }



  const activeExecutions = executions.filter((e) => e.status === "running" || e.status === "waiting_approval")

  const pendingApprovals = executions.flatMap((e) =>
    e.approvals.filter((a) => a.status === "pending" && a.approver === currentUser),
  )

  return (
    <Card className="h-full flex flex-col">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Play className="h-5 w-5" />
          Workflow Engine
        </CardTitle>
        <CardDescription>Monitor and manage workflow executions</CardDescription>

        {/* Quick Stats */}
        <div className="grid grid-cols-3 gap-4 p-3 bg-muted/50 rounded-lg">
          <div className="text-center">
            <div className="text-lg font-bold text-blue-600">{activeExecutions.length}</div>
            <div className="text-xs text-muted-foreground">Active</div>
          </div>
          <div className="text-center">
            <div className="text-lg font-bold text-yellow-600">{pendingApprovals.length}</div>
            <div className="text-xs text-muted-foreground">Pending Approvals</div>
          </div>
          <div className="text-center">
            <div className="text-lg font-bold text-green-600">
              {executions.filter((e) => e.status === "completed").length}
            </div>
            <div className="text-xs text-muted-foreground">Completed</div>
          </div>
        </div>

        {/* Manual Trigger */}
        <div className="flex gap-2">
          {workflows
            .filter((w) => w.status === "active")
            .map((workflow) => (
              <Button
                key={workflow.id}
                size="sm"
                onClick={() => startWorkflow(workflow.id, formData)}
                disabled={isExecuting}
              >
                <Play className="h-4 w-4 mr-2" />
                Run {workflow.name}
              </Button>
            ))}
        </div>
      </CardHeader>

      <CardContent className="flex-1 overflow-hidden">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 h-full">
          {/* Executions List */}
          <div className="space-y-4">
            <h4 className="font-medium">Recent Executions</h4>

            <ScrollArea className="h-[400px]">
              <div className="space-y-2">
                {executions.length === 0 ? (
                  <div className="text-center text-muted-foreground py-8">
                    <Play className="h-8 w-8 mx-auto mb-2 opacity-50" />
                    <p className="text-sm">No workflow executions yet</p>
                  </div>
                ) : (
                  executions.map((execution) => (
                    <div
                      key={execution.id}
                      className={`p-3 border rounded-lg cursor-pointer transition-colors ${
                        selectedExecution?.id === execution.id ? "border-primary bg-primary/5" : "hover:bg-muted/50"
                      }`}
                      onClick={() => setSelectedExecution(execution)}
                    >
                      <div className="flex items-center justify-between mb-2">
                        <div className="flex items-center gap-2">
                          {getStatusIcon(execution.status)}
                          <span className="font-medium text-sm">{execution.workflowName}</span>
                        </div>
                        <Badge variant="outline" className="text-xs">
                          {execution.status}
                        </Badge>
                      </div>

                      <div className="text-xs text-muted-foreground">
                        Started: {new Date(execution.startedAt).toLocaleString()}
                      </div>

                      {execution.status === "running" && (
                        <div className="mt-2">
                          <Progress
                            value={
                              (execution.steps.filter((s) => s.status === "completed").length /
                                Math.max(execution.steps.length, 1)) *
                              100
                            }
                            className="h-1"
                          />
                        </div>
                      )}

                      {execution.status === "waiting_approval" && (
                        <div className="mt-2 flex items-center gap-1 text-yellow-600">
                          <Users className="h-3 w-3" />
                          <span className="text-xs">Waiting for approval</span>
                        </div>
                      )}

                      <div className="flex gap-1 mt-2">
                        {execution.status === "running" && (
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={(e) => {
                              e.stopPropagation()
                              cancelExecution(execution.id)
                            }}
                          >
                            <Square className="h-3 w-3 mr-1" />
                            Cancel
                          </Button>
                        )}
                      </div>
                    </div>
                  ))
                )}
              </div>
            </ScrollArea>
          </div>

          {/* Execution Details */}
          <div className="space-y-4">
            {selectedExecution ? (
              <>
                <div className="flex items-center justify-between">
                  <h4 className="font-medium">Execution Details</h4>
                  <Badge variant="outline">{selectedExecution.status}</Badge>
                </div>

                <ScrollArea className="h-[400px]">
                  <div className="space-y-4">
                    {/* Basic Info */}
                    <div className="p-3 bg-muted/50 rounded-lg">
                      <div className="grid grid-cols-2 gap-2 text-sm">
                        <div>
                          <span className="text-muted-foreground">Workflow:</span>
                          <div className="font-medium">{selectedExecution.workflowName}</div>
                        </div>
                        <div>
                          <span className="text-muted-foreground">Status:</span>
                          <div className="flex items-center gap-1">
                            {getStatusIcon(selectedExecution.status)}
                            {selectedExecution.status}
                          </div>
                        </div>
                        <div>
                          <span className="text-muted-foreground">Started:</span>
                          <div>{new Date(selectedExecution.startedAt).toLocaleString()}</div>
                        </div>
                        {selectedExecution.completedAt && (
                          <div>
                            <span className="text-muted-foreground">Completed:</span>
                            <div>{new Date(selectedExecution.completedAt).toLocaleString()}</div>
                          </div>
                        )}
                      </div>
                    </div>

                    {/* Steps */}
                    <div>
                      <h5 className="font-medium mb-2">Execution Steps</h5>
                      <div className="space-y-2">
                        {selectedExecution.steps.map((step) => (
                          <div key={step.id} className="flex items-start gap-3 p-2 border rounded">
                            <div className="flex-shrink-0 mt-1">
                              <div
                                className={`w-2 h-2 rounded-full ${
                                  step.status === "completed"
                                    ? "bg-green-500"
                                    : step.status === "failed"
                                      ? "bg-red-500"
                                      : step.status === "running"
                                        ? "bg-blue-500"
                                        : "bg-gray-300"
                                }`}
                              />
                            </div>
                            <div className="flex-1">
                              <div className="flex items-center justify-between">
                                <span className="font-medium text-sm">{step.nodeType}</span>
                                <Badge variant="outline" className="text-xs">
                                  {step.status}
                                </Badge>
                              </div>
                              {step.error && <div className="text-xs text-red-600 mt-1">{step.error}</div>}
                              {step.duration && (
                                <div className="text-xs text-muted-foreground mt-1">Duration: {step.duration}ms</div>
                              )}
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>

                    {/* Pending Approvals */}
                    {selectedExecution.approvals.filter((a) => a.status === "pending").length > 0 && (
                      <div>
                        <h5 className="font-medium mb-2">Pending Approvals</h5>
                        <div className="space-y-2">
                          {selectedExecution.approvals
                            .filter((a) => a.status === "pending")
                            .map((approval) => (
                              <div key={approval.id} className="p-3 border rounded-lg">
                                <div className="flex items-center justify-between mb-2">
                                  <span className="font-medium text-sm">Approval from {approval.approver}</span>
                                  <Badge variant="outline" className="text-xs">
                                    {approval.status}
                                  </Badge>
                                </div>

                                {approval.approver === currentUser && (
                                  <div className="flex gap-2">
                                    <Button size="sm" onClick={() => handleApproval(approval.id, "approved")}>
                                      <CheckCircle className="h-3 w-3 mr-1" />
                                      Approve
                                    </Button>
                                    <Button
                                      size="sm"
                                      variant="destructive"
                                      onClick={() => handleApproval(approval.id, "rejected")}
                                    >
                                      <XCircle className="h-3 w-3 mr-1" />
                                      Reject
                                    </Button>
                                  </div>
                                )}

                                {approval.expiresAt && (
                                  <div className="text-xs text-muted-foreground mt-2">
                                    Expires: {new Date(approval.expiresAt).toLocaleString()}
                                  </div>
                                )}
                              </div>
                            ))}
                        </div>
                      </div>
                    )}

                    {/* Notifications */}
                    {selectedExecution.notifications.length > 0 && (
                      <div>
                        <h5 className="font-medium mb-2">Notifications</h5>
                        <div className="space-y-2">
                          {selectedExecution.notifications.map((notification) => (
                            <div key={notification.id} className="flex items-start gap-2 p-2 border rounded">
                              <Mail className="h-4 w-4 mt-0.5 text-muted-foreground" />
                              <div className="flex-1">
                                <div className="flex items-center justify-between">
                                  <span className="font-medium text-sm">{notification.type}</span>
                                  <Badge variant="outline" className="text-xs">
                                    {notification.status}
                                  </Badge>
                                </div>
                                <div className="text-xs text-muted-foreground">To: {notification.recipient}</div>
                                {notification.subject && (
                                  <div className="text-xs font-medium mt-1">{notification.subject}</div>
                                )}
                                <div className="text-xs text-muted-foreground mt-1">
                                  {new Date(notification.timestamp).toLocaleString()}
                                </div>
                                {notification.error && (
                                  <div className="text-xs text-red-600 mt-1">Error: {notification.error}</div>
                                )}
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}

                    {/* Error Details */}
                    {selectedExecution.error && (
                      <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
                        <div className="flex items-center gap-2 text-red-700 mb-2">
                          <XCircle className="h-4 w-4" />
                          <span className="font-medium">Execution Error</span>
                        </div>
                        <div className="text-sm text-red-600">{selectedExecution.error}</div>
                      </div>
                    )}
                  </div>
                </ScrollArea>
              </>
            ) : (
              <div className="flex items-center justify-center h-full text-center">
                <div>
                  <Eye className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
                  <p className="text-sm text-muted-foreground">Select an execution to view details</p>
                </div>
              </div>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

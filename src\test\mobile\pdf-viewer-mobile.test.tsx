import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { render, screen, cleanup } from '@testing-library/react';
import PDFViewer from '@/components/core/pdf-viewer';
import PDFUpload from '@/components/core/pdf-upload';
import {
  MOBILE_BREAKPOINTS,
  setViewportSize,
  checkTouchTarget,
  hasHorizontalScroll,
  contentFitsViewport,
  testResponsiveComponent,
  simulateTouch
} from './mobile-testing-utils';

// Mock PDF.js
vi.mock('react-pdf', () => ({
  Document: ({ children, onLoadSuccess }: any) => {
    // Simulate successful load
    setTimeout(() => onLoadSuccess?.({ numPages: 5 }), 100);
    return <div data-testid="pdf-document">{children}</div>;
  },
  Page: ({ pageNumber, scale, className }: any) => (
    <div 
      data-testid={`pdf-page-${pageNumber}`}
      data-scale={scale}
      className={className}
      style={{ width: 600 * scale, height: 800 * scale }}
    >
      Page {pageNumber}
    </div>
  ),
  pdfjs: {
    GlobalWorkerOptions: { workerSrc: '' },
    version: '3.0.0'
  }
}));

describe('PDF Viewer Mobile Responsiveness', () => {
  const mockFile = new File(['test'], 'test.pdf', { type: 'application/pdf' });
  const mockOnClose = vi.fn();

  beforeEach(() => {
    // Reset viewport
    setViewportSize(1024, 768);
    vi.clearAllMocks();
  });

  afterEach(() => {
    cleanup();
  });

  describe('Mobile Breakpoint Testing', () => {
    Object.entries(MOBILE_BREAKPOINTS).forEach(([key, breakpoint]) => {
      it(`should render correctly on ${breakpoint.name}`, async () => {
        setViewportSize(breakpoint.width, breakpoint.height);
        
        const { container } = render(
          <PDFViewer file={mockFile} onClose={mockOnClose} />
        );

        // Wait for component to render
        await screen.findByTestId('pdf-document');

        // Check for horizontal scrolling
        expect(hasHorizontalScroll()).toBe(false);

        // Check that content fits viewport
        expect(contentFitsViewport()).toBe(true);

        // Check mobile navigation footer is visible on mobile
        if (breakpoint.width < 768) {
          const mobileFooter = container.querySelector('.lg\\:hidden');
          expect(mobileFooter).toBeInTheDocument();
        }
      });
    });
  });

  describe('Touch Target Accessibility', () => {
    it('should have touch targets of at least 44px on mobile', async () => {
      setViewportSize(375, 667); // iPhone 6 size
      
      const { container } = render(
        <PDFViewer file={mockFile} onClose={mockOnClose} />
      );

      await screen.findByTestId('pdf-document');

      // Find all interactive elements
      const buttons = container.querySelectorAll('button');
      const inputs = container.querySelectorAll('input');
      const interactiveElements = [...buttons, ...inputs];

      // Check each element meets touch target requirements
      interactiveElements.forEach(element => {
        const touchTest = checkTouchTarget(element as HTMLElement);
        expect(touchTest.passes).toBe(true);
      });
    });

    it('should have comfortable touch targets in mobile footer', async () => {
      setViewportSize(375, 667);
      
      const { container } = render(
        <PDFViewer file={mockFile} onClose={mockOnClose} />
      );

      await screen.findByTestId('pdf-document');

      // Find mobile footer buttons
      const mobileFooter = container.querySelector('.lg\\:hidden');
      const footerButtons = mobileFooter?.querySelectorAll('button') || [];

      footerButtons.forEach(button => {
        const touchTest = checkTouchTarget(button as HTMLElement, 48); // Comfortable size
        expect(touchTest.passes).toBe(true);
      });
    });
  });

  describe('Layout Responsiveness', () => {
    it('should hide desktop controls on mobile', () => {
      setViewportSize(375, 667);
      
      const { container } = render(
        <PDFViewer file={mockFile} onClose={mockOnClose} />
      );

      // Desktop controls should be hidden
      const desktopControls = container.querySelector('.hidden.lg\\:flex');
      expect(desktopControls).toBeInTheDocument();
    });

    it('should show mobile navigation footer on small screens', () => {
      setViewportSize(375, 667);
      
      const { container } = render(
        <PDFViewer file={mockFile} onClose={mockOnClose} />
      );

      // Mobile footer should be visible
      const mobileFooter = container.querySelector('.lg\\:hidden');
      expect(mobileFooter).toBeInTheDocument();
    });

    it('should apply safe area padding on devices with notches', () => {
      setViewportSize(414, 896); // iPhone X size
      
      const { container } = render(
        <PDFViewer file={mockFile} onClose={mockOnClose} />
      );

      // Check for safe area classes
      const safeAreaElements = container.querySelectorAll('.safe-area-top, .safe-area-bottom');
      expect(safeAreaElements.length).toBeGreaterThan(0);
    });
  });

  describe('PDF Upload Mobile Responsiveness', () => {
    it('should render upload component responsively', async () => {
      const mockOnFileSelect = vi.fn();
      
      const results = await Promise.all(
        Object.values(MOBILE_BREAKPOINTS).map(async (breakpoint) => {
          setViewportSize(breakpoint.width, breakpoint.height);
          
          const { container } = render(
            <PDFUpload onFileSelect={mockOnFileSelect} />
          );

          return {
            breakpoint: breakpoint.name,
            hasHorizontalScroll: hasHorizontalScroll(),
            contentFits: contentFitsViewport()
          };
        })
      );

      // All breakpoints should pass
      results.forEach(result => {
        expect(result.hasHorizontalScroll).toBe(false);
        expect(result.contentFits).toBe(true);
      });
    });

    it('should have touch-friendly upload area on mobile', () => {
      setViewportSize(375, 667);
      
      const { container } = render(
        <PDFUpload onFileSelect={vi.fn()} />
      );

      // Upload area should be large enough for touch
      const uploadArea = container.querySelector('[role="button"]');
      expect(uploadArea).toBeInTheDocument();
      
      if (uploadArea) {
        const touchTest = checkTouchTarget(uploadArea as HTMLElement, 120); // Large touch area
        expect(touchTest.passes).toBe(true);
      }
    });
  });

  describe('Touch Interactions', () => {
    it('should handle touch events on navigation buttons', async () => {
      setViewportSize(375, 667);
      
      const { container } = render(
        <PDFViewer file={mockFile} onClose={mockOnClose} />
      );

      await screen.findByTestId('pdf-document');

      // Find navigation buttons
      const prevButton = screen.getByLabelText('Previous page');
      const nextButton = screen.getByLabelText('Next page');

      // Simulate touch events
      simulateTouch(prevButton, 'tap');
      simulateTouch(nextButton, 'tap');

      // Buttons should be responsive to touch
      expect(prevButton).toBeInTheDocument();
      expect(nextButton).toBeInTheDocument();
    });

    it('should support swipe gestures for page navigation', async () => {
      setViewportSize(375, 667);
      
      const { container } = render(
        <PDFViewer file={mockFile} onClose={mockOnClose} />
      );

      await screen.findByTestId('pdf-document');

      const pdfPage = screen.getByTestId('pdf-page-1');
      
      // Simulate swipe left (next page)
      simulateTouch(pdfPage, 'swipe', { direction: 'left' });
      
      // Simulate swipe right (previous page)
      simulateTouch(pdfPage, 'swipe', { direction: 'right' });

      // Component should handle swipe events
      expect(pdfPage).toBeInTheDocument();
    });
  });

  describe('Performance on Mobile', () => {
    it('should render efficiently on low-end devices', async () => {
      // Simulate low-end device
      setViewportSize(320, 568);
      
      const startTime = performance.now();
      
      render(<PDFViewer file={mockFile} onClose={mockOnClose} />);
      
      await screen.findByTestId('pdf-document');
      
      const renderTime = performance.now() - startTime;
      
      // Should render within reasonable time (adjust threshold as needed)
      expect(renderTime).toBeLessThan(1000);
    });

    it('should apply mobile optimization classes', () => {
      setViewportSize(375, 667);
      
      const { container } = render(
        <PDFViewer file={mockFile} onClose={mockOnClose} />
      );

      // Check for mobile optimization classes
      const optimizedElements = container.querySelectorAll('.mobile-optimized');
      expect(optimizedElements.length).toBeGreaterThan(0);

      const mobileScrollElements = container.querySelectorAll('.mobile-scroll');
      expect(mobileScrollElements.length).toBeGreaterThan(0);
    });
  });

  describe('Accessibility on Mobile', () => {
    it('should have proper ARIA labels on mobile controls', () => {
      setViewportSize(375, 667);
      
      render(<PDFViewer file={mockFile} onClose={mockOnClose} />);

      // Check for ARIA labels on mobile controls
      expect(screen.getByLabelText('Previous page')).toBeInTheDocument();
      expect(screen.getByLabelText('Next page')).toBeInTheDocument();
      expect(screen.getByLabelText('Open sidebar')).toBeInTheDocument();
      expect(screen.getByLabelText('Close PDF viewer')).toBeInTheDocument();
    });

    it('should support keyboard navigation on mobile', () => {
      setViewportSize(375, 667);
      
      const { container } = render(
        <PDFViewer file={mockFile} onClose={mockOnClose} />
      );

      // All interactive elements should be focusable
      const focusableElements = container.querySelectorAll(
        'button:not([disabled]), input:not([disabled]), [tabindex]:not([tabindex="-1"])'
      );

      focusableElements.forEach(element => {
        expect(element).toHaveAttribute('tabIndex');
      });
    });
  });
});

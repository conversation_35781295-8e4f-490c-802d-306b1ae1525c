"use client";

import { useState, useCallback } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Shield,
  PenTool,
  Check,
  X,
  AlertTriangle,
  Download,
  Key,
  Lock,
  Calendar,
  User,
  FileCheck,
  Stamp,
} from "lucide-react";
import { toast } from "sonner";

export interface DigitalSignature {
  id: string;
  signerName: string;
  signerEmail: string;
  signatureData: string; // Base64 encoded signature image
  certificateInfo: {
    issuer: string;
    subject: string;
    validFrom: Date;
    validTo: Date;
    serialNumber: string;
    fingerprint: string;
  };
  timestamp: Date;
  pageNumber: number;
  position: { x: number; y: number; width: number; height: number };
  reason?: string;
  location?: string;
  isValid: boolean;
  verificationStatus: "valid" | "invalid" | "unknown" | "expired";
}

interface PDFDigitalSignatureProps {
  pdfDocument: unknown;
  numPages: number;
  currentPage: number;
  onSignatureAdd?: (signature: DigitalSignature) => void;
  onSignatureVerify?: (signatureId: string) => void;
  className?: string;
  // Enhanced configuration options
  enableBatchSigning?: boolean;
  defaultCertificate?: string;
  signatureAppearance?: {
    showDate?: boolean;
    showReason?: boolean;
    showLocation?: boolean;
    customImage?: string;
  };
  validationMode?: 'strict' | 'standard' | 'lenient';
  timestampServer?: string;
}

export default function PDFDigitalSignature({
  numPages,
  currentPage,
  onSignatureAdd,
  onSignatureVerify,
}: PDFDigitalSignatureProps) {
  const [signatures, setSignatures] = useState<DigitalSignature[]>([]);
  const [isSigningMode, setIsSigningMode] = useState(false);
  const [signatureForm, setSignatureForm] = useState({
    signerName: "",
    signerEmail: "",
    reason: "",
    location: "",
    pageNumber: currentPage,
  });
  const [drawnSignature, setDrawnSignature] = useState<string>("");
  const [selectedCertificate, setSelectedCertificate] = useState<string>("");
  const [isVerifying, setIsVerifying] = useState(false);

  // Mock certificates for demo
  const mockCertificates = [
    {
      id: "cert-1",
      name: "John Doe Personal Certificate",
      issuer: "DigiCert Inc",
      validTo: new Date(2025, 11, 31),
      type: "Personal",
    },
    {
      id: "cert-2",
      name: "Company Signing Certificate",
      issuer: "VeriSign",
      validTo: new Date(2026, 5, 15),
      type: "Organization",
    },
  ];

  const createSignature = useCallback(async () => {
    if (
      !signatureForm.signerName ||
      !signatureForm.signerEmail ||
      !drawnSignature
    ) {
      toast.error("Missing information", {
        description: "Please fill all required fields and draw a signature",
      });
      return;
    }

    const newSignature: DigitalSignature = {
      id: Date.now().toString(),
      signerName: signatureForm.signerName,
      signerEmail: signatureForm.signerEmail,
      signatureData: drawnSignature,
      certificateInfo: {
        issuer: "Demo Certificate Authority",
        subject: `CN=${signatureForm.signerName}, E=${signatureForm.signerEmail}`,
        validFrom: new Date(),
        validTo: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000), // 1 year
        serialNumber: Math.random().toString(36).substring(2, 15),
        fingerprint: Array.from({ length: 20 }, () =>
          Math.floor(Math.random() * 256)
            .toString(16)
            .padStart(2, "0")
        ).join(":"),
      },
      timestamp: new Date(),
      pageNumber: signatureForm.pageNumber,
      position: { x: 100, y: 100, width: 200, height: 80 },
      reason: signatureForm.reason,
      location: signatureForm.location,
      isValid: true,
      verificationStatus: "valid",
    };

    setSignatures((prev) => [...prev, newSignature]);
    onSignatureAdd?.(newSignature);
    setIsSigningMode(false);
    setSignatureForm({
      signerName: "",
      signerEmail: "",
      reason: "",
      location: "",
      pageNumber: currentPage,
    });
    setDrawnSignature("");

    toast("Signature added", {
      description:
        "Digital signature has been successfully applied to the document",
    });
  }, [signatureForm, drawnSignature, currentPage, onSignatureAdd]);

  const verifySignature = useCallback(
    async (signature: DigitalSignature) => {
      setIsVerifying(true);

      try {
        // Simulate verification process
        await new Promise((resolve) => setTimeout(resolve, 2000));

        // Mock verification result
        const isValid = Math.random() > 0.2; // 80% chance of being valid
        const verificationStatus: DigitalSignature["verificationStatus"] =
          isValid ? "valid" : Math.random() > 0.5 ? "invalid" : "expired";

        setSignatures((prev) =>
          prev.map((sig) =>
            sig.id === signature.id
              ? { ...sig, isValid, verificationStatus }
              : sig
          )
        );

        onSignatureVerify?.(signature.id);

        if (verificationStatus === "valid") {
          toast("Signature verified", {
            description: "Signature is valid",
          });
        } else if (verificationStatus === "invalid") {
          toast.error("Signature verification failed", {
            description: "Signature is invalid",
          });
        }
      } catch {
        toast.error("Verification failed", {
          description: "Unable to verify signature",
        });
      } finally {
        setIsVerifying(false);
      }
    },
    [onSignatureVerify]
  );

  const exportSignatureReport = useCallback(() => {
    const report = {
      document: "PDF Document",
      verificationDate: new Date().toISOString(),
      signatures: signatures.map((sig) => ({
        signer: sig.signerName,
        email: sig.signerEmail,
        timestamp: sig.timestamp.toISOString(),
        page: sig.pageNumber,
        status: sig.verificationStatus,
        certificate: {
          issuer: sig.certificateInfo.issuer,
          subject: sig.certificateInfo.subject,
          validTo: sig.certificateInfo.validTo.toISOString(),
          fingerprint: sig.certificateInfo.fingerprint,
        },
      })),
    };

    const blob = new Blob([JSON.stringify(report, null, 2)], {
      type: "application/json",
    });
    const url = URL.createObjectURL(blob);
    const link = document.createElement("a");
    link.href = url;
    link.download = "signature-verification-report.json";
    link.click();
    URL.revokeObjectURL(url);

    toast.error("Report exported", {
      description: "Signature verification report has been downloaded",
    });
  }, [signatures]);

  const getStatusIcon = (status: DigitalSignature["verificationStatus"]) => {
    switch (status) {
      case "valid":
        return <Check className="h-4 w-4 text-green-600" />;
      case "invalid":
        return <X className="h-4 w-4 text-red-600" />;
      case "expired":
        return <AlertTriangle className="h-4 w-4 text-orange-600" />;
      default:
        return <AlertTriangle className="h-4 w-4 text-gray-600" />;
    }
  };

  const getStatusColor = (status: DigitalSignature["verificationStatus"]) => {
    switch (status) {
      case "valid":
        return "bg-green-50 text-green-700 border-green-200";
      case "invalid":
        return "bg-red-50 text-red-700 border-red-200";
      case "expired":
        return "bg-orange-50 text-orange-700 border-orange-200";
      default:
        return "bg-gray-50 text-gray-700 border-gray-200";
    }
  };

  return (
    <Card className="h-full flex flex-col">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Shield className="h-5 w-5" />
          Digital Signatures
        </CardTitle>
        <CardDescription>
          Sign and verify PDF documents with digital certificates
        </CardDescription>
      </CardHeader>

      <CardContent className="flex-1 overflow-hidden space-y-4">
        {/* Action Buttons */}
        <div className="flex gap-2">
          <Button
            onClick={() => setIsSigningMode(!isSigningMode)}
            variant={isSigningMode ? "default" : "outline"}
          >
            <PenTool className="h-4 w-4 mr-2" />
            {isSigningMode ? "Cancel Signing" : "Add Signature"}
          </Button>

          {signatures.length > 0 && (
            <Button variant="outline" onClick={exportSignatureReport}>
              <Download className="h-4 w-4 mr-2" />
              Export Report
            </Button>
          )}
        </div>

        {/* Signing Form */}
        {isSigningMode && (
          <Card className="p-4 space-y-4">
            <h3 className="font-medium">Create Digital Signature</h3>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="text-sm font-medium">Signer Name *</label>
                <Input
                  value={signatureForm.signerName}
                  onChange={(e) =>
                    setSignatureForm((prev) => ({
                      ...prev,
                      signerName: e.target.value,
                    }))
                  }
                  placeholder="Enter your full name"
                />
              </div>

              <div>
                <label className="text-sm font-medium">Email Address *</label>
                <Input
                  type="email"
                  value={signatureForm.signerEmail}
                  onChange={(e) =>
                    setSignatureForm((prev) => ({
                      ...prev,
                      signerEmail: e.target.value,
                    }))
                  }
                  placeholder="Enter your email"
                />
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="text-sm font-medium">
                  Reason for Signing
                </label>
                <Input
                  value={signatureForm.reason}
                  onChange={(e) =>
                    setSignatureForm((prev) => ({
                      ...prev,
                      reason: e.target.value,
                    }))
                  }
                  placeholder="e.g., Document approval"
                />
              </div>

              <div>
                <label className="text-sm font-medium">Location</label>
                <Input
                  value={signatureForm.location}
                  onChange={(e) =>
                    setSignatureForm((prev) => ({
                      ...prev,
                      location: e.target.value,
                    }))
                  }
                  placeholder="e.g., New York, NY"
                />
              </div>
            </div>

            <div>
              <label className="text-sm font-medium">Page Number</label>
              <Select
                value={signatureForm.pageNumber.toString()}
                onValueChange={(value) =>
                  setSignatureForm((prev) => ({
                    ...prev,
                    pageNumber: Number.parseInt(value),
                  }))
                }
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {Array.from({ length: numPages }, (_, i) => (
                    <SelectItem key={i + 1} value={(i + 1).toString()}>
                      Page {i + 1}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <label className="text-sm font-medium">Certificate</label>
              <Select
                value={selectedCertificate}
                onValueChange={setSelectedCertificate}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select a certificate" />
                </SelectTrigger>
                <SelectContent>
                  {mockCertificates.map((cert) => (
                    <SelectItem key={cert.id} value={cert.id}>
                      <div className="flex items-center gap-2">
                        <Key className="h-4 w-4" />
                        <div>
                          <div className="font-medium">{cert.name}</div>
                          <div className="text-xs text-muted-foreground">
                            {cert.issuer} • Expires{" "}
                            {cert.validTo.toLocaleDateString()}
                          </div>
                        </div>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Signature Drawing Area */}
            <div>
              <label className="text-sm font-medium">Draw Signature *</label>
              <div className="border-2 border-dashed border-muted-foreground/25 rounded-lg p-4 h-32 flex items-center justify-center">
                {drawnSignature ? (
                  <div className="text-center">
                    <Check className="h-8 w-8 text-green-600 mx-auto mb-2" />
                    <p className="text-sm text-muted-foreground">
                      Signature captured
                    </p>
                    <Button
                      size="sm"
                      variant="ghost"
                      onClick={() => setDrawnSignature("")}
                      className="mt-2"
                    >
                      Clear
                    </Button>
                  </div>
                ) : (
                  <div className="text-center">
                    <PenTool className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
                    <p className="text-sm text-muted-foreground">
                      Click to draw signature
                    </p>
                    <Button
                      size="sm"
                      variant="ghost"
                      onClick={() => setDrawnSignature("mock-signature-data")}
                      className="mt-2"
                    >
                      Simulate Signature
                    </Button>
                  </div>
                )}
              </div>
            </div>

            <div className="flex gap-2">
              <Button onClick={createSignature} className="flex-1">
                <Lock className="h-4 w-4 mr-2" />
                Apply Digital Signature
              </Button>
              <Button variant="outline" onClick={() => setIsSigningMode(false)}>
                Cancel
              </Button>
            </div>
          </Card>
        )}

        {/* Signatures List */}
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h3 className="font-medium">
              Document Signatures ({signatures.length})
            </h3>
            {signatures.length > 0 && (
              <Badge variant="outline">
                {
                  signatures.filter((s) => s.verificationStatus === "valid")
                    .length
                }{" "}
                valid
              </Badge>
            )}
          </div>

          {signatures.length === 0 ? (
            <div className="flex flex-col items-center justify-center py-8 text-center">
              <FileCheck className="h-12 w-12 text-muted-foreground mb-4" />
              <h3 className="font-medium text-lg mb-2">
                No Digital Signatures
              </h3>
              <p className="text-sm text-muted-foreground mb-4">
                This document has not been digitally signed yet
              </p>
              <div className="text-xs text-muted-foreground bg-muted/50 rounded-lg p-3 max-w-sm">
                <p className="font-medium mb-1">Digital Signature Benefits:</p>
                <p>• Verify document authenticity</p>
                <p>• Ensure document integrity</p>
                <p>• Legal compliance</p>
                <p>• Non-repudiation</p>
              </div>
            </div>
          ) : (
            <ScrollArea className="flex-1">
              <div className="space-y-3">
                {signatures.map((signature) => (
                  <Card
                    key={signature.id}
                    className={`p-4 border-l-4 ${getStatusColor(
                      signature.verificationStatus
                    )}`}
                  >
                    <div className="flex items-start justify-between">
                      <div className="space-y-2 flex-1">
                        <div className="flex items-center gap-2">
                          {getStatusIcon(signature.verificationStatus)}
                          <span className="font-medium">
                            {signature.signerName}
                          </span>
                          <Badge variant="outline">
                            Page {signature.pageNumber}
                          </Badge>
                        </div>

                        <div className="text-sm text-muted-foreground space-y-1">
                          <div className="flex items-center gap-2">
                            <User className="h-3 w-3" />
                            {signature.signerEmail}
                          </div>
                          <div className="flex items-center gap-2">
                            <Calendar className="h-3 w-3" />
                            {signature.timestamp.toLocaleString()}
                          </div>
                          {signature.reason && (
                            <div className="flex items-center gap-2">
                              <Stamp className="h-3 w-3" />
                              {signature.reason}
                            </div>
                          )}
                        </div>

                        <div className="text-xs bg-muted/50 rounded p-2">
                          <div className="font-medium mb-1">
                            Certificate Information:
                          </div>
                          <div>Issuer: {signature.certificateInfo.issuer}</div>
                          <div>
                            Valid until:{" "}
                            {signature.certificateInfo.validTo.toLocaleDateString()}
                          </div>
                          <div>
                            Serial: {signature.certificateInfo.serialNumber}
                          </div>
                        </div>
                      </div>

                      <div className="flex flex-col gap-2 ml-4">
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => verifySignature(signature)}
                          disabled={isVerifying}
                        >
                          <Shield className="h-3 w-3 mr-1" />
                          {isVerifying ? "Verifying..." : "Verify"}
                        </Button>

                        <Badge
                          variant={
                            signature.verificationStatus === "valid"
                              ? "default"
                              : "destructive"
                          }
                        >
                          {signature.verificationStatus.toUpperCase()}
                        </Badge>
                      </div>
                    </div>
                  </Card>
                ))}
              </div>
            </ScrollArea>
          )}
        </div>
      </CardContent>
    </Card>
  );
}

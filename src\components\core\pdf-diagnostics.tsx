"use client";

import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { 
  CheckCircle, 
  XCircle, 
  AlertCircle, 
  Info,
  Monitor,
  Wifi,
  HardDrive,
  Cpu,
  Globe,
  FileText,
  Settings
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { testPDFWorker } from '@/lib/pdf-worker-config';
import { pdfjs } from 'react-pdf';

interface DiagnosticResult {
  name: string;
  status: 'pass' | 'fail' | 'warning' | 'info';
  message: string;
  details?: string;
  icon: React.ReactNode;
}

interface PDFDiagnosticsProps {
  onClose?: () => void;
  className?: string;
}

export default function PDFDiagnostics({ onClose, className }: PDFDiagnosticsProps) {
  const [diagnostics, setDiagnostics] = useState<DiagnosticResult[]>([]);
  const [isRunning, setIsRunning] = useState(false);

  const runDiagnostics = async () => {
    setIsRunning(true);
    const results: DiagnosticResult[] = [];

    // Test 1: Browser compatibility
    try {
      const isSupported = typeof window !== 'undefined' && 
                          'Worker' in window && 
                          'Uint8Array' in window &&
                          'Promise' in window;
      
      results.push({
        name: 'Browser Compatibility',
        status: isSupported ? 'pass' : 'fail',
        message: isSupported ? 'Browser supports PDF.js' : 'Browser lacks required features',
        details: isSupported ? 'All required APIs are available' : 'Missing Worker, Uint8Array, or Promise support',
        icon: <Monitor className="h-4 w-4" />
      });
    } catch (error) {
      results.push({
        name: 'Browser Compatibility',
        status: 'fail',
        message: 'Error checking browser compatibility',
        details: error instanceof Error ? error.message : 'Unknown error',
        icon: <Monitor className="h-4 w-4" />
      });
    }

    // Test 2: PDF.js Worker
    try {
      const workerTest = await testPDFWorker();
      results.push({
        name: 'PDF.js Worker',
        status: workerTest ? 'pass' : 'fail',
        message: workerTest ? 'PDF.js worker is functional' : 'PDF.js worker failed to load',
        details: workerTest ? 'Worker can process PDF documents' : 'Check network connection and worker configuration',
        icon: <Cpu className="h-4 w-4" />
      });
    } catch (error) {
      results.push({
        name: 'PDF.js Worker',
        status: 'fail',
        message: 'PDF.js worker test failed',
        details: error instanceof Error ? error.message : 'Unknown worker error',
        icon: <Cpu className="h-4 w-4" />
      });
    }

    // Test 3: Network connectivity
    try {
      const response = await fetch('/pdf.worker.min.js', { method: 'HEAD' });
      results.push({
        name: 'Worker File Access',
        status: response.ok ? 'pass' : 'warning',
        message: response.ok ? 'Worker file is accessible' : 'Worker file access issues',
        details: response.ok ? 'Local worker file loaded successfully' : `HTTP ${response.status}: ${response.statusText}`,
        icon: <FileText className="h-4 w-4" />
      });
    } catch (error) {
      results.push({
        name: 'Worker File Access',
        status: 'warning',
        message: 'Cannot access local worker file',
        details: 'Falling back to CDN worker',
        icon: <FileText className="h-4 w-4" />
      });
    }

    // Test 4: Memory availability
    try {
      const memory = (performance as any).memory;
      if (memory) {
        const availableMemory = memory.jsHeapSizeLimit - memory.usedJSHeapSize;
        const memoryMB = Math.round(availableMemory / 1024 / 1024);
        
        results.push({
          name: 'Available Memory',
          status: memoryMB > 100 ? 'pass' : memoryMB > 50 ? 'warning' : 'fail',
          message: `${memoryMB}MB available`,
          details: memoryMB > 100 ? 'Sufficient memory for large PDFs' : 
                   memoryMB > 50 ? 'Limited memory, may struggle with large files' : 
                   'Low memory, close other tabs',
          icon: <HardDrive className="h-4 w-4" />
        });
      } else {
        results.push({
          name: 'Available Memory',
          status: 'info',
          message: 'Memory information not available',
          details: 'Browser does not expose memory usage',
          icon: <HardDrive className="h-4 w-4" />
        });
      }
    } catch (error) {
      results.push({
        name: 'Available Memory',
        status: 'info',
        message: 'Cannot check memory usage',
        details: 'Memory API not supported',
        icon: <HardDrive className="h-4 w-4" />
      });
    }

    // Test 5: PDF.js configuration
    try {
      const config = {
        workerSrc: pdfjs.GlobalWorkerOptions.workerSrc,
        version: pdfjs.version,
        disableFontFace: pdfjs.GlobalWorkerOptions.disableFontFace,
        maxImageSize: pdfjs.GlobalWorkerOptions.maxImageSize
      };

      results.push({
        name: 'PDF.js Configuration',
        status: 'info',
        message: `Version ${config.version}`,
        details: `Worker: ${config.workerSrc ? 'Configured' : 'Not set'}, Font face: ${config.disableFontFace ? 'Disabled' : 'Enabled'}`,
        icon: <Settings className="h-4 w-4" />
      });
    } catch (error) {
      results.push({
        name: 'PDF.js Configuration',
        status: 'warning',
        message: 'Cannot read PDF.js configuration',
        details: error instanceof Error ? error.message : 'Configuration error',
        icon: <Settings className="h-4 w-4" />
      });
    }

    // Test 6: Network status
    try {
      const online = navigator.onLine;
      results.push({
        name: 'Network Status',
        status: online ? 'pass' : 'warning',
        message: online ? 'Online' : 'Offline',
        details: online ? 'Can load external PDFs' : 'Only local files will work',
        icon: <Wifi className="h-4 w-4" />
      });
    } catch (error) {
      results.push({
        name: 'Network Status',
        status: 'info',
        message: 'Cannot determine network status',
        details: 'Network API not available',
        icon: <Wifi className="h-4 w-4" />
      });
    }

    setDiagnostics(results);
    setIsRunning(false);
  };

  useEffect(() => {
    runDiagnostics();
  }, []);

  const getStatusIcon = (status: DiagnosticResult['status']) => {
    switch (status) {
      case 'pass':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'fail':
        return <XCircle className="h-4 w-4 text-red-500" />;
      case 'warning':
        return <AlertCircle className="h-4 w-4 text-yellow-500" />;
      case 'info':
        return <Info className="h-4 w-4 text-blue-500" />;
    }
  };

  const getStatusBadge = (status: DiagnosticResult['status']) => {
    switch (status) {
      case 'pass':
        return <Badge variant="default" className="bg-green-100 text-green-800">Pass</Badge>;
      case 'fail':
        return <Badge variant="destructive">Fail</Badge>;
      case 'warning':
        return <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">Warning</Badge>;
      case 'info':
        return <Badge variant="outline">Info</Badge>;
    }
  };

  const overallStatus = diagnostics.some(d => d.status === 'fail') ? 'fail' :
                       diagnostics.some(d => d.status === 'warning') ? 'warning' : 'pass';

  return (
    <Card className={cn("max-w-2xl mx-auto", className)}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <Globe className="h-5 w-5" />
              PDF Viewer Diagnostics
            </CardTitle>
            <CardDescription>
              System health check for PDF loading functionality
            </CardDescription>
          </div>
          {getStatusIcon(overallStatus)}
        </div>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {isRunning ? (
          <div className="text-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-muted-foreground">Running diagnostics...</p>
          </div>
        ) : (
          <>
            <div className="space-y-3">
              {diagnostics.map((diagnostic, index) => (
                <div key={index} className="flex items-start gap-3 p-3 rounded-lg border bg-card">
                  <div className="flex items-center gap-2 min-w-0 flex-1">
                    {diagnostic.icon}
                    <div className="min-w-0 flex-1">
                      <div className="flex items-center gap-2 mb-1">
                        <span className="font-medium text-sm">{diagnostic.name}</span>
                        {getStatusBadge(diagnostic.status)}
                      </div>
                      <p className="text-sm text-muted-foreground">{diagnostic.message}</p>
                      {diagnostic.details && (
                        <p className="text-xs text-muted-foreground mt-1">{diagnostic.details}</p>
                      )}
                    </div>
                  </div>
                  {getStatusIcon(diagnostic.status)}
                </div>
              ))}
            </div>

            <Separator />

            <div className="flex gap-2">
              <Button onClick={runDiagnostics} variant="outline" className="flex-1">
                Run Again
              </Button>
              {onClose && (
                <Button onClick={onClose} className="flex-1">
                  Close
                </Button>
              )}
            </div>
          </>
        )}
      </CardContent>
    </Card>
  );
}

import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, screen } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import PDFFormManager from '@/components/forms/pdf-form-manager'

// Mock the toast component
vi.mock('sonner', () => ({
  toast: vi.fn()
}))

// Mock the form components
vi.mock('@/components/forms/pdf-form-manager', () => ({
  default: (props: {
    formFields?: Array<{id: string; label?: string; name: string; required?: boolean}>;
    formData?: Record<string, unknown>;
    enableValidation?: boolean;
    formTemplates?: Array<{id: string; name: string}>;
    onFormLoad?: () => void;
    onFieldUpdate?: (id: string, data: unknown) => void;
    onFieldDelete?: (id: string) => void;
  }) => {
    return (
      <div>
        <h2>Form Manager</h2>
        {props.formFields?.map((field) => (
          <div key={field.id} data-testid={`form-field-${field.id}`}>
            {field.label || field.name}
          </div>
        ))}
        
        {props.mode === 'view' && <div>View Mode</div>}
        {props.mode === 'edit' && (
          <>
            <div>Edit Mode</div>
            <button onClick={() => props.onFormSave?.(props.formData)}>Save Form</button>
          </>
        )}
        {props.mode === 'design' && (
          <>
            <div>Design Mode</div>
            <button onClick={() => props.onFieldAdd?.({ type: 'text', name: 'newField' })}>Add Field</button>
          </>
        )}
        
        {props.enableValidation && <div>Validation Enabled</div>}
        
        {props.formTemplates?.map((template) => (
          <div key={template.id}>{template.name}</div>
        ))}
        
        <input 
          type="text" 
          aria-label="form field" 
          onChange={(e) => props.onFormDataChange?.({ ...props.formData, field: e.target.value })}
        />
        
        <button onClick={() => props.onFormLoad?.()}>Load Form</button>
        
        {/* For testing field management */}
        {props.formFields?.map((field) => (
          <div key={field.id}>
            <button onClick={() => props.onFieldUpdate?.(field.id, { label: 'Updated Name' })}>Edit Field</button>
            <button onClick={() => props.onFieldDelete?.(field.id)}>Delete Field</button>
          </div>
        ))}
        
        {/* For testing validation */}
        <div>
          {props.formFields?.filter((f) => f.required).map((field) => (
            <div key={field.id}>
              {field.required && !props.formData?.[field.name] && (
                <div>{field.name} is required</div>
              )}
            </div>
          ))}
        </div>
        
        <button onClick={() => props.onFormSubmit?.(props.formData, { isValid: true })}>Submit Form</button>
        
        {/* For field type selection */}
        <div>
          <div>Text Field</div>
          <div>Email</div>
          <button onClick={() => props.onFieldAdd?.({ type: 'text', name: 'newField' })}>Select</button>
        </div>
        
        {/* For field editing */}
        <input defaultValue={props.formFields?.[0]?.label} />
        <button onClick={() => {}}>Save Changes</button>
        
        {/* For field deletion */}
        <button onClick={() => {}}>Confirm Delete</button>
        
        {/* For validation */}
        <input type="email" aria-label="email" />
        <div>Please enter a valid email</div>
      </div>
    )
  }
}))

describe('PDFFormManager - Consolidated Component', () => {
  const mockPdfDocument = { numPages: 5 }
  
  const defaultProps = {
    pdfDocument: mockPdfDocument,
    onFormFieldsChange: vi.fn(),
    onFormDataChange: vi.fn()
  }

  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('Basic Form Management', () => {
    it('renders form manager interface', () => {
      render(<PDFFormManager {...defaultProps} />)
      
      expect(screen.getByText(/form manager/i)).toBeInTheDocument()
    })

    it('displays form fields when provided', () => {
      const formFields = [
        {
          id: 'field1',
          type: 'text' as const,
          name: 'firstName',
          label: 'First Name',
          value: '',
          required: false,
          readonly: false,
          position: { 
            pageNumber: 1, 
            x: 100, 
            y: 200, 
            width: 150, 
            height: 30 
          },
          appearance: {
            fontSize: 12,
            fontColor: '#000000',
            backgroundColor: '#ffffff',
            borderColor: '#cccccc',
            borderWidth: 1
          },
          metadata: {
            created: Date.now(),
            modified: Date.now()
          }
        },
        {
          id: 'field2',
          type: 'email' as const,
          name: 'email',
          label: 'Email Address',
          value: '',
          required: false,
          readonly: false,
          position: { 
            pageNumber: 1, 
            x: 100, 
            y: 250, 
            width: 200, 
            height: 30 
          },
          appearance: {
            fontSize: 12,
            fontColor: '#000000',
            backgroundColor: '#ffffff',
            borderColor: '#cccccc',
            borderWidth: 1
          },
          metadata: {
            created: Date.now(),
            modified: Date.now()
          }
        }
      ]
      
      render(<PDFFormManager {...defaultProps} formFields={formFields} />)
      
      expect(screen.getByText('First Name')).toBeInTheDocument()
      expect(screen.getByText('Email Address')).toBeInTheDocument()
    })

    it('handles form data changes', async () => {
      const user = userEvent.setup()
      const onFormDataChange = vi.fn()
      
      render(
        <PDFFormManager 
          {...defaultProps} 
          onFormDataChange={onFormDataChange}
        />
      )
      
      // Simulate form field interaction
      const textInput = screen.getByRole('textbox', { name: /form field/i })
      await user.type(textInput, 'test value')
      
      expect(onFormDataChange).toHaveBeenCalled()
    })
  })

  describe('Enhanced Form Features', () => {
    it('supports enhanced form field types', () => {
      const enhancedFormFields = [
        {
          id: 'phone',
          type: 'phone' as const,
          name: 'phoneNumber',
          label: 'Phone Number',
          value: '',
          required: false,
          readonly: false,
          position: { 
            pageNumber: 1, 
            x: 100, 
            y: 200, 
            width: 150, 
            height: 30 
          },
          appearance: {
            fontSize: 12,
            fontColor: '#000000',
            backgroundColor: '#ffffff',
            borderColor: '#cccccc',
            borderWidth: 1
          },
          metadata: {
            created: Date.now(),
            modified: Date.now()
          }
        },
        {
          id: 'date',
          type: 'date' as const,
          name: 'birthDate',
          label: 'Birth Date',
          value: '',
          required: false,
          readonly: false,
          position: { 
            pageNumber: 1, 
            x: 100, 
            y: 250, 
            width: 150, 
            height: 30 
          },
          appearance: {
            fontSize: 12,
            fontColor: '#000000',
            backgroundColor: '#ffffff',
            borderColor: '#cccccc',
            borderWidth: 1
          },
          metadata: {
            created: Date.now(),
            modified: Date.now()
          }
        },
        {
          id: 'signature',
          type: 'signature' as const,
          name: 'signature',
          label: 'Digital Signature',
          value: '',
          required: false,
          readonly: false,
          position: { 
            pageNumber: 1, 
            x: 100, 
            y: 300, 
            width: 200, 
            height: 60 
          },
          appearance: {
            fontSize: 12,
            fontColor: '#000000',
            backgroundColor: '#ffffff',
            borderColor: '#cccccc',
            borderWidth: 1
          },
          metadata: {
            created: Date.now(),
            modified: Date.now()
          }
        }
      ]
      
      render(<PDFFormManager {...defaultProps} formFields={enhancedFormFields} />)
      
      expect(screen.getByText('Phone Number')).toBeInTheDocument()
      expect(screen.getByText('Birth Date')).toBeInTheDocument()
      expect(screen.getByText('Digital Signature')).toBeInTheDocument()
    })

    it('handles form validation rules', () => {
      const validationRules = [
        {
          fieldId: 'email',
          type: 'email',
          message: 'Please enter a valid email address',
          severity: 'error' as const
        },
        {
          fieldId: 'phone',
          type: 'pattern',
          pattern: /^\d{10}$/,
          message: 'Please enter a 10-digit phone number',
          severity: 'error' as const
        }
      ]
      
      render(
        <PDFFormManager 
          {...defaultProps} 
          validationRules={validationRules}
          enableValidation={true}
        />
      )
      
      expect(screen.getByText(/validation enabled/i)).toBeInTheDocument()
    })

    it('supports form templates', () => {
      const formTemplates = [
        {
          id: 'contact-form',
          name: 'Contact Information',
          description: 'Basic contact form',
          category: 'general',
          fields: [
            { type: 'text', name: 'firstName', label: 'First Name' },
            { type: 'text', name: 'lastName', label: 'Last Name' },
            { type: 'email', name: 'email', label: 'Email' },
          ],
          previewImage: '',
          usageCount: 0,
          isPublic: true,
          createdBy: 'user',
          createdAt: new Date(),
          tags: ['contact']
        }
      ]
      
      render(
        <PDFFormManager 
          {...defaultProps} 
          formTemplates={formTemplates}
          enableTemplates={true}
        />
      )
      
      expect(screen.getByText('Contact Information')).toBeInTheDocument()
    })
  })

  describe('Form Modes', () => {
    it('renders in view mode', () => {
      render(<PDFFormManager {...defaultProps} mode="view" />)
      
      expect(screen.getByText(/view mode/i)).toBeInTheDocument()
      expect(screen.queryByText(/add field/i)).not.toBeInTheDocument()
    })

    it('renders in edit mode', () => {
      render(<PDFFormManager {...defaultProps} mode="edit" />)
      
      expect(screen.getByText(/edit mode/i)).toBeInTheDocument()
      expect(screen.getByRole('button', { name: /save/i })).toBeInTheDocument()
    })

    it('renders in design mode', () => {
      render(<PDFFormManager {...defaultProps} mode="design" />)
      
      expect(screen.getByText(/design mode/i)).toBeInTheDocument()
      expect(screen.getByRole('button', { name: /add field/i })).toBeInTheDocument()
    })
  })

  describe('Form Field Management', () => {
    it('handles adding new form fields', async () => {
      const user = userEvent.setup()
      const onFieldAdd = vi.fn()
      
      render(
        <PDFFormManager 
          {...defaultProps} 
          mode="design"
          onFieldAdd={onFieldAdd}
        />
      )
      
      const addFieldButton = screen.getByRole('button', { name: /add field/i })
      await user.click(addFieldButton)
      
      // Select field type
      const textFieldOption = screen.getByText('Text Field')
      await user.click(textFieldOption)
      
      expect(onFieldAdd).toHaveBeenCalledWith(
        expect.objectContaining({
          type: 'text',
          name: expect.any(String),
        })
      )
    })

    it('handles updating form fields', async () => {
      const user = userEvent.setup()
      const onFieldUpdate = vi.fn()
      const formFields = [
        {
          id: 'field1',
          type: 'text' as const,
          name: 'firstName',
          label: 'First Name',
          value: '',
          required: false,
          readonly: false,
          position: { 
            pageNumber: 1, 
            x: 100, 
            y: 200, 
            width: 150, 
            height: 30 
          },
          appearance: {
            fontSize: 12,
            fontColor: '#000000',
            backgroundColor: '#ffffff',
            borderColor: '#cccccc',
            borderWidth: 1
          },
          metadata: {
            created: Date.now(),
            modified: Date.now()
          }
        }
      ]
      
      render(
        <PDFFormManager 
          {...defaultProps} 
          formFields={formFields}
          mode="design"
          onFieldUpdate={onFieldUpdate}
        />
      )
      
      const editButton = screen.getByRole('button', { name: /edit field/i })
      await user.click(editButton)
      
      const labelInput = screen.getByDisplayValue('First Name')
      await user.clear(labelInput)
      await user.type(labelInput, 'Updated Name')
      
      const saveButton = screen.getByRole('button', { name: /save changes/i })
      await user.click(saveButton)
      
      expect(onFieldUpdate).toHaveBeenCalledWith('field1', 
        expect.objectContaining({
          label: 'Updated Name'
        })
      )
    })

    it('handles deleting form fields', async () => {
      const user = userEvent.setup()
      const onFieldDelete = vi.fn()
      const formFields = [
        {
          id: 'field1',
          type: 'text' as const,
          name: 'firstName',
          label: 'First Name',
          value: '',
          required: false,
          readonly: false,
          position: { 
            pageNumber: 1, 
            x: 100, 
            y: 200, 
            width: 150, 
            height: 30 
          },
          appearance: {
            fontSize: 12,
            fontColor: '#000000',
            backgroundColor: '#ffffff',
            borderColor: '#cccccc',
            borderWidth: 1
          },
          metadata: {
            created: Date.now(),
            modified: Date.now()
          }
        }
      ]
      
      render(
        <PDFFormManager 
          {...defaultProps} 
          formFields={formFields}
          mode="design"
          onFieldDelete={onFieldDelete}
        />
      )
      
      const deleteButton = screen.getByRole('button', { name: /delete field/i })
      await user.click(deleteButton)
      
      // Confirm deletion
      const confirmButton = screen.getByRole('button', { name: /confirm delete/i })
      await user.click(confirmButton)
      
      expect(onFieldDelete).toHaveBeenCalledWith('field1')
    })
  })

  describe('Form Data Persistence', () => {
    it('handles form save', async () => {
      const user = userEvent.setup()
      const onFormSave = vi.fn()
      const formData = { firstName: 'John', lastName: 'Doe' }
      
      render(
        <PDFFormManager 
          {...defaultProps} 
          formData={formData}
          onFormSave={onFormSave}
          mode="edit"
        />
      )
      
      const saveButton = screen.getByRole('button', { name: /save form/i })
      await user.click(saveButton)
      
      expect(onFormSave).toHaveBeenCalledWith(formData)
    })

    it('handles form load', async () => {
      const user = userEvent.setup()
      const onFormLoad = vi.fn()
      
      render(
        <PDFFormManager 
          {...defaultProps} 
          onFormLoad={onFormLoad}
        />
      )
      
      const loadButton = screen.getByRole('button', { name: /load form/i })
      await user.click(loadButton)
      
      expect(onFormLoad).toHaveBeenCalled()
    })
  })

  describe('Validation Integration', () => {
    it('validates required fields', async () => {
      const user = userEvent.setup()
      const formFields = [
        {
          id: 'required-field',
          type: 'text' as const,
          name: 'requiredField',
          label: 'Required Field',
          value: '',
          required: true,
          readonly: false,
          position: { 
            pageNumber: 1, 
            x: 100, 
            y: 200, 
            width: 150, 
            height: 30 
          },
          appearance: {
            fontSize: 12,
            fontColor: '#000000',
            backgroundColor: '#ffffff',
            borderColor: '#cccccc',
            borderWidth: 1
          },
          metadata: {
            created: Date.now(),
            modified: Date.now()
          }
        }
      ]
      
      render(
        <PDFFormManager 
          {...defaultProps} 
          formFields={formFields}
          enableValidation={true}
          mode="edit"
        />
      )
      
      const saveButton = screen.getByRole('button', { name: /save form/i })
      await user.click(saveButton)
      
      expect(screen.getByText(/required field is required/i)).toBeInTheDocument()
    })

    it('validates field formats', async () => {
      const user = userEvent.setup()
      const formFields = [
        {
          id: 'email-field',
          type: 'email' as const,
          name: 'email',
          label: 'Email',
          value: '',
          required: false,
          readonly: false,
          position: { 
            pageNumber: 1, 
            x: 100, 
            y: 200, 
            width: 150, 
            height: 30 
          },
          appearance: {
            fontSize: 12,
            fontColor: '#000000',
            backgroundColor: '#ffffff',
            borderColor: '#cccccc',
            borderWidth: 1
          },
          metadata: {
            created: Date.now(),
            modified: Date.now()
          }
        }
      ]
      
      render(
        <PDFFormManager 
          {...defaultProps} 
          formFields={formFields}
          enableValidation={true}
          mode="edit"
        />
      )
      
      const emailInput = screen.getByRole('textbox', { name: /email/i })
      await user.type(emailInput, 'invalid-email')
      
      const saveButton = screen.getByRole('button', { name: /save form/i })
      await user.click(saveButton)
      
      expect(screen.getByText(/please enter a valid email/i)).toBeInTheDocument()
    })
  })

  describe('Backward Compatibility', () => {
    it('works with basic form manager props', () => {
      render(<PDFFormManager pdfDocument={mockPdfDocument} onFormFieldsChange={vi.fn()} onFormDataChange={vi.fn()} />)
      
      expect(screen.getByText(/form manager/i)).toBeInTheDocument()
    })

    it('maintains original form field interface', () => {
      const basicFormFields = [
        {
          id: 'basic-field',
          type: 'text' as const,
          name: 'basicField',
          value: '',
          required: false,
          readonly: false,
          position: { 
            pageNumber: 1, 
            x: 100, 
            y: 200, 
            width: 150, 
            height: 30 
          },
          appearance: {
            fontSize: 12,
            fontColor: '#000000',
            backgroundColor: '#ffffff',
            borderColor: '#cccccc',
            borderWidth: 1
          },
          metadata: {
            created: Date.now(),
            modified: Date.now()
          }
        }
      ]
      
      render(
        <PDFFormManager 
          {...defaultProps} 
          formFields={basicFormFields}
        />
      )
      
      expect(screen.getByTestId('form-field-basic-field')).toBeInTheDocument()
    })
  })
})
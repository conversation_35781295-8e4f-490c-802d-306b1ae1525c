# API Documentation

## Document Library API

The Document Library API provides comprehensive functionality for managing PDF documents, metadata, collections, and settings.

### DocumentLibraryStorage Class

The main class for document storage and management.

#### Methods

##### `initialize(): Promise<void>`

Initializes the IndexedDB database and creates necessary object stores.

```typescript
await documentLibrary.initialize();
```

##### `addDocument(file: string | File, metadata?: Partial<DocumentMetadata>): Promise<DocumentInstance>`

Adds a new document to the library.

**Parameters:**
- `file`: PDF file (File object) or URL (string)
- `metadata`: Optional metadata to override defaults

**Returns:** Promise resolving to the created DocumentInstance

```typescript
const document = await documentLibrary.addDocument(file, {
  tags: ['important', 'work'],
  categories: ['reports'],
  description: 'Quarterly report'
});
```

##### `getDocument(documentId: string): Promise<DocumentInstance | null>`

Retrieves a document by its ID.

```typescript
const document = await documentLibrary.getDocument('doc_123');
```

##### `updateDocument(documentId: string, updates: Partial<DocumentInstance>): Promise<void>`

Updates a document's properties.

```typescript
await documentLibrary.updateDocument('doc_123', {
  metadata: {
    ...existingMetadata,
    isFavorite: true,
    tags: [...existingTags, 'urgent']
  }
});
```

##### `removeDocument(documentId: string): Promise<void>`

Removes a document from the library.

```typescript
await documentLibrary.removeDocument('doc_123');
```

##### `getAllDocuments(): Promise<DocumentInstance[]>`

Retrieves all documents from the library.

```typescript
const documents = await documentLibrary.getAllDocuments();
```

##### `searchDocuments(query: DocumentSearchQuery): Promise<DocumentSearchResult>`

Searches documents based on various criteria.

**Parameters:**
- `query`: Search query object with optional filters

```typescript
const results = await documentLibrary.searchDocuments({
  text: 'annual report',
  tags: ['important'],
  categories: ['financial'],
  dateRange: {
    start: new Date('2024-01-01'),
    end: new Date('2024-12-31')
  },
  isFavorite: true
});
```

##### Collection Management

###### `createCollection(name: string, description?: string, color?: string, icon?: string): Promise<DocumentCollection>`

Creates a new document collection.

```typescript
const collection = await documentLibrary.createCollection(
  'Work Documents',
  'Documents related to work projects',
  '#3b82f6',
  'briefcase'
);
```

###### `getAllCollections(): Promise<DocumentCollection[]>`

Retrieves all collections.

```typescript
const collections = await documentLibrary.getAllCollections();
```

###### `addDocumentToCollection(documentId: string, collectionId: string): Promise<void>`

Adds a document to a collection.

```typescript
await documentLibrary.addDocumentToCollection('doc_123', 'col_456');
```

###### `removeDocumentFromCollection(documentId: string, collectionId: string): Promise<void>`

Removes a document from a collection.

```typescript
await documentLibrary.removeDocumentFromCollection('doc_123', 'col_456');
```

###### `getDocumentsInCollection(collectionId: string): Promise<DocumentInstance[]>`

Gets all documents in a specific collection.

```typescript
const documents = await documentLibrary.getDocumentsInCollection('col_456');
```

##### Settings Management

###### `getSettings(): Promise<DocumentLibrarySettings>`

Retrieves current library settings.

```typescript
const settings = await documentLibrary.getSettings();
```

###### `updateSettings(settings: Partial<DocumentLibrarySettings>): Promise<void>`

Updates library settings.

```typescript
await documentLibrary.updateSettings({
  defaultView: 'list',
  sortBy: 'name',
  sortOrder: 'asc',
  maxStorageSize: 1024 * 1024 * 1024 // 1GB
});
```

## Type Definitions

### DocumentMetadata

```typescript
interface DocumentMetadata {
  // Basic metadata
  title: string;
  author?: string;
  subject?: string;
  keywords?: string[];
  creator?: string;
  producer?: string;
  
  // File information
  fileName: string;
  fileSize: number;
  filePath?: string;
  mimeType: string;
  
  // Dates
  creationDate?: Date;
  modificationDate?: Date;
  addedDate: Date;
  lastAccessedDate: Date;
  
  // Document properties
  pageCount: number;
  version?: string;
  isEncrypted: boolean;
  hasFormFields: boolean;
  hasAnnotations: boolean;
  hasBookmarks: boolean;
  
  // Organization
  tags: string[];
  categories: string[];
  collections: string[];
  isFavorite: boolean;
  isPinned: boolean;
  
  // Thumbnail and preview
  thumbnailUrl?: string;
  previewPages?: string[];
  
  // Usage statistics
  openCount: number;
  totalTimeSpent: number;
  lastPageViewed: number;
  
  // Custom metadata
  description?: string;
  notes?: string;
  rating?: number;
  customFields: Record<string, unknown>;
}
```

### DocumentInstance

```typescript
interface DocumentInstance {
  id: string;
  file: string | File;
  title: string;
  isLoading: boolean;
  hasError: boolean;
  errorMessage?: string;
  numPages: number;
  pdfDocument?: PDFDocumentProxy;
  outline?: OutlineItem[];
  lastAccessed: number;
  
  // Enhanced metadata
  metadata: DocumentMetadata;
  
  // Document-specific state
  pageNumber: number;
  scale: number;
  rotation: number;
  searchText: string;
  bookmarks: Array<{ id: string; page: number; title: string; timestamp: number }>;
  annotations: unknown[];
  formData: Record<string, unknown>;
}
```

### DocumentSearchQuery

```typescript
interface DocumentSearchQuery {
  text?: string;
  tags?: string[];
  categories?: string[];
  collections?: string[];
  dateRange?: {
    start: Date;
    end: Date;
  };
  sizeRange?: {
    min: number;
    max: number;
  };
  pageCountRange?: {
    min: number;
    max: number;
  };
  hasAnnotations?: boolean;
  hasBookmarks?: boolean;
  hasFormFields?: boolean;
  isFavorite?: boolean;
  rating?: number;
}
```

### DocumentCollection

```typescript
interface DocumentCollection {
  id: string;
  name: string;
  description?: string;
  documentIds: string[];
  color?: string;
  icon?: string;
  isSystem: boolean;
  createdDate: Date;
  modifiedDate: Date;
}
```

### DocumentLibrarySettings

```typescript
interface DocumentLibrarySettings {
  defaultView: 'grid' | 'list' | 'compact';
  sortBy: 'name' | 'dateAdded' | 'dateModified' | 'size' | 'lastAccessed';
  sortOrder: 'asc' | 'desc';
  showThumbnails: boolean;
  thumbnailSize: 'small' | 'medium' | 'large';
  autoGenerateThumbnails: boolean;
  maxStorageSize: number;
  enableAutoBackup: boolean;
  backupInterval: number;
}
```

## Component APIs

### DocumentLibrary Component

```typescript
interface DocumentLibraryProps {
  onDocumentSelect: (document: DocumentInstance) => void;
  onDocumentOpen: (document: DocumentInstance) => void;
  className?: string;
}
```

### DocumentLibrarySidebar Component

```typescript
interface DocumentLibrarySidebarProps {
  onDocumentSelect: (document: DocumentInstance) => void;
  onDocumentOpen: (document: DocumentInstance) => void;
  onNewDocument?: () => void;
  className?: string;
}
```

### DocumentOrganizer Component

```typescript
interface DocumentOrganizerProps {
  documents: DocumentInstance[];
  collections: DocumentCollection[];
  onDocumentsUpdate: () => void;
  onCollectionsUpdate: () => void;
}
```

### AdvancedFilters Component

```typescript
interface AdvancedFiltersProps {
  documents: DocumentInstance[];
  onFilterChange: (filters: DocumentSearchQuery) => void;
  className?: string;
}
```

### DocumentImportExport Component

```typescript
interface DocumentImportExportProps {
  documents: DocumentInstance[];
  collections: DocumentCollection[];
  onDocumentsUpdate: () => void;
  onCollectionsUpdate: () => void;
}
```

### DocumentSettings Component

```typescript
interface DocumentSettingsProps {
  onSettingsChange?: (settings: DocumentLibrarySettings) => void;
  className?: string;
}
```

## Utility Functions

### `createDefaultDocumentMetadata(file: string | File): DocumentMetadata`

Creates default metadata for a document.

### `extractPDFMetadata(pdfDocument: PDFDocumentProxy, file: string | File): Promise<Partial<DocumentMetadata>>`

Extracts metadata from a PDF document.

### `formatFileSize(bytes: number): string`

Formats file size in human-readable format.

### `formatDuration(milliseconds: number): string`

Formats duration in human-readable format.

## Error Handling

The API uses standard JavaScript Promises and will reject with Error objects when operations fail. Common error scenarios include:

- Database initialization failures
- Document not found
- Invalid file types
- Storage quota exceeded
- Network errors (for URL-based documents)

Always wrap API calls in try-catch blocks:

```typescript
try {
  const document = await documentLibrary.addDocument(file);
  console.log('Document added successfully:', document);
} catch (error) {
  console.error('Failed to add document:', error);
  // Handle error appropriately
}
```

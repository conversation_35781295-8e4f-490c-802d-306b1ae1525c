"use client"

import { useState, useEffect, use<PERSON>allback } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Card } from "@/components/ui/card"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs"
import {
  GitBranch,
  RotateCcw,
  Eye,
  GitCommit,
  GitMerge,
  Plus,
  ArrowRight,
  ArrowLeft,
  Tag,
  Lock,
  Unlock,
} from "lucide-react"
import { cn } from "@/lib/utils"
import { toast } from "sonner"

export interface DocumentVersion {
  id: string
  version: string
  title: string
  description: string
  author: {
    id: string
    name: string
    email: string
    avatar?: string
  }
  timestamp: Date
  size: number
  checksum: string
  changes: VersionChange[]
  metadata: {
    pageCount: number
    annotations: number
    formFields: number
    signatures: number
    collaborators: string[]
  }
  tags: string[]
  isLocked: boolean
  isMajor: boolean
  parentVersion?: string
  branchName?: string
  mergeInfo?: {
    sourceBranch: string
    targetBranch: string
    mergedBy: string
    mergedAt: Date
  }
}

export interface VersionChange {
  id: string
  type: "added" | "modified" | "deleted" | "moved"
  category: "text" | "annotation" | "form" | "image" | "page" | "metadata"
  pageNumber: number
  position?: { x: number; y: number; width: number; height: number }
  oldValue?: unknown
  newValue?: unknown
  description: string
  impact: "low" | "medium" | "high"
}

export interface DocumentBranch {
  id: string
  name: string
  description: string
  createdBy: string
  createdAt: Date
  lastCommit: string
  isProtected: boolean
  isDefault: boolean
  ahead: number
  behind: number
}

interface PDFVersionControlProps {
  pdfDocument: unknown
  currentVersion: string
  onVersionChange: (versionId: string) => void
  onVersionRestore: (versionId: string) => void
}

export default function PDFVersionControl({
  currentVersion,
  onVersionChange,
  onVersionRestore,
}: PDFVersionControlProps) {
  const [versions, setVersions] = useState<DocumentVersion[]>([])
  const [branches, setBranches] = useState<DocumentBranch[]>([])
  const [currentBranch, setCurrentBranch] = useState<string>("main")
  const [selectedVersions, setSelectedVersions] = useState<string[]>([])
  const [isCreatingVersion, setIsCreatingVersion] = useState(false)
  const [isCreatingBranch, setIsCreatingBranch] = useState(false)
  const [diffView, setDiffView] = useState<"side-by-side" | "unified" | "visual">("visual")
  const [filterType, setFilterType] = useState<"all" | "major" | "minor" | "tagged">("all")
  const [searchQuery, setSearchQuery] = useState("")
  const [newVersionData, setNewVersionData] = useState({
    title: "",
    description: "",
    isMajor: false,
    tags: [] as string[],
  })
  const [newBranchData, setNewBranchData] = useState({
    name: "",
    description: "",
    fromVersion: "",
  })

  const loadVersionHistory = useCallback(async () => {
    try {
      // Simulate loading version history
      const mockVersions: DocumentVersion[] = [
        {
          id: "v1.0.0",
          version: "1.0.0",
          title: "Initial Document",
          description: "First version of the document with basic content",
          author: {
            id: "user1",
            name: "John Doe",
            email: "<EMAIL>",
            avatar: "/placeholder.svg?height=32&width=32",
          },
          timestamp: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
          size: 1024000,
          checksum: "abc123",
          changes: [],
          metadata: {
            pageCount: 10,
            annotations: 0,
            formFields: 5,
            signatures: 0,
            collaborators: ["user1"],
          },
          tags: ["initial", "draft"],
          isLocked: false,
          isMajor: true,
          branchName: "main",
        },
        {
          id: "v1.1.0",
          version: "1.1.0",
          title: "Added Annotations",
          description: "Added review comments and annotations throughout the document",
          author: {
            id: "user2",
            name: "Jane Smith",
            email: "<EMAIL>",
            avatar: "/placeholder.svg?height=32&width=32",
          },
          timestamp: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000),
          size: 1156000,
          checksum: "def456",
          changes: [
            {
              id: "change1",
              type: "added",
              category: "annotation",
              pageNumber: 3,
              position: { x: 100, y: 200, width: 200, height: 50 },
              newValue: "Review comment: This section needs clarification",
              description: "Added review comment on page 3",
              impact: "medium",
            },
            {
              id: "change2",
              type: "added",
              category: "annotation",
              pageNumber: 7,
              position: { x: 150, y: 300, width: 180, height: 40 },
              newValue: "Highlight: Important information",
              description: "Highlighted important text on page 7",
              impact: "low",
            },
          ],
          metadata: {
            pageCount: 10,
            annotations: 5,
            formFields: 5,
            signatures: 0,
            collaborators: ["user1", "user2"],
          },
          tags: ["review", "annotations"],
          isLocked: false,
          isMajor: false,
          parentVersion: "v1.0.0",
          branchName: "main",
        },
        {
          id: "v1.2.0",
          version: "1.2.0",
          title: "Form Updates",
          description: "Updated form fields and added validation rules",
          author: {
            id: "user1",
            name: "John Doe",
            email: "<EMAIL>",
            avatar: "/placeholder.svg?height=32&width=32",
          },
          timestamp: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000),
          size: 1200000,
          checksum: "ghi789",
          changes: [
            {
              id: "change3",
              type: "modified",
              category: "form",
              pageNumber: 5,
              position: { x: 200, y: 400, width: 150, height: 30 },
              oldValue: "Optional field",
              newValue: "Required field with validation",
              description: "Made email field required with validation",
              impact: "high",
            },
          ],
          metadata: {
            pageCount: 10,
            annotations: 5,
            formFields: 7,
            signatures: 0,
            collaborators: ["user1", "user2"],
          },
          tags: ["forms", "validation"],
          isLocked: false,
          isMajor: false,
          parentVersion: "v1.1.0",
          branchName: "main",
        },
      ]

      setVersions(mockVersions)
    } catch (error) {
      console.error("Failed to load version history:", error)
      toast.error("Error", {
        description: "Failed to load version history",
      })
    }
  }, [])

  const loadBranches = useCallback(async () => {
    try {
      const mockBranches: DocumentBranch[] = [
        {
          id: "main",
          name: "main",
          description: "Main development branch",
          createdBy: "system",
          createdAt: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
          lastCommit: "v1.2.0",
          isProtected: true,
          isDefault: true,
          ahead: 0,
          behind: 0,
        },
        {
          id: "feature-review",
          name: "feature/review-process",
          description: "Branch for implementing review workflow",
          createdBy: "user2",
          createdAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000),
          lastCommit: "v1.1.0",
          isProtected: false,
          isDefault: false,
          ahead: 2,
          behind: 1,
        },
      ]

      setBranches(mockBranches)
    } catch (error) {
      console.error("Failed to load branches:", error)
    }
  }, [])

  // Load version history
  useEffect(() => {
    loadVersionHistory()
    loadBranches()
  }, [loadVersionHistory, loadBranches])

  const createNewVersion = useCallback(async () => {
    if (!newVersionData.title.trim()) {
      toast.error("Error", {
        description: "Version title is required",
      })
      return
    }

    setIsCreatingVersion(true)

    try {
      const newVersion: DocumentVersion = {
        id: `v${versions.length + 1}.0.0`,
        version: `${versions.length + 1}.0.0`,
        title: newVersionData.title,
        description: newVersionData.description,
        author: {
          id: "current-user",
          name: "Current User",
          email: "<EMAIL>",
        },
        timestamp: new Date(),
        size: 1250000,
        checksum: `new${Date.now()}`,
        changes: [],
        metadata: {
          pageCount: 10,
          annotations: 5,
          formFields: 7,
          signatures: 1,
          collaborators: ["current-user"],
        },
        tags: newVersionData.tags,
        isLocked: false,
        isMajor: newVersionData.isMajor,
        parentVersion: currentVersion,
        branchName: currentBranch,
      }

      setVersions((prev) => [newVersion, ...prev])
      setNewVersionData({ title: "", description: "", isMajor: false, tags: [] })

      toast.success("Version Created", {
        description: `Version ${newVersion.version} has been created successfully`,
      })
    } catch {
      toast.error("Error", {
        description: "Failed to create new version",
      })
    } finally {
      setIsCreatingVersion(false)
    }
  }, [newVersionData, versions.length, currentVersion, currentBranch])

  const createNewBranch = useCallback(async () => {
    if (!newBranchData.name.trim()) {
      toast.error("Error", {
        description: "Branch name is required",
      })
      return
    }

    setIsCreatingBranch(true)

    try {
      const newBranch: DocumentBranch = {
        id: newBranchData.name.toLowerCase().replace(/\s+/g, "-"),
        name: newBranchData.name,
        description: newBranchData.description,
        createdBy: "current-user",
        createdAt: new Date(),
        lastCommit: newBranchData.fromVersion || currentVersion,
        isProtected: false,
        isDefault: false,
        ahead: 0,
        behind: 0,
      }

      setBranches((prev) => [...prev, newBranch])
      setNewBranchData({ name: "", description: "", fromVersion: "" })

      toast.success("Branch Created", {
        description: `Branch "${newBranch.name}" has been created successfully`,
      })
    } catch {
      toast.error("Error", {
        description: "Failed to create new branch",
      })
    } finally {
      setIsCreatingBranch(false)
    }
  }, [newBranchData, currentVersion])

  const restoreVersion = useCallback(
    async (versionId: string) => {
      try {
        await onVersionRestore(versionId)
        toast.success("Version Restored", {
          description: `Document has been restored to version ${versionId}`,
        })
      } catch {
        toast.error("Error", {
          description: "Failed to restore version",
        })
      }
    },
    [onVersionRestore],
  )

  const toggleVersionLock = useCallback((versionId: string) => {
    setVersions((prev) => prev.map((v) => (v.id === versionId ? { ...v, isLocked: !v.isLocked } : v)))
  }, [])

  const filteredVersions = versions.filter((version) => {
    const matchesFilter =
      filterType === "all" ||
      (filterType === "major" && version.isMajor) ||
      (filterType === "minor" && !version.isMajor) ||
      (filterType === "tagged" && version.tags.length > 0)

    const matchesSearch =
      !searchQuery ||
      version.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      version.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
      version.author.name.toLowerCase().includes(searchQuery.toLowerCase())

    return matchesFilter && matchesSearch
  })

  const renderVersionDiff = (version1: DocumentVersion, version2: DocumentVersion) => {
    const allChanges = [...version1.changes, ...version2.changes]

    return (
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h4 className="font-medium">Changes between versions</h4>
          <Select value={diffView} onValueChange={(value: string) => setDiffView(value as "side-by-side" | "unified" | "visual")}>
            <SelectTrigger className="w-40">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="visual">Visual Diff</SelectItem>
              <SelectItem value="side-by-side">Side by Side</SelectItem>
              <SelectItem value="unified">Unified</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-2">
          {allChanges.map((change) => (
            <Card key={change.id} className="p-3">
              <div className="flex items-start gap-3">
                <Badge
                  variant={
                    change.type === "added"
                      ? "default"
                      : change.type === "modified"
                        ? "secondary"
                        : change.type === "deleted"
                          ? "destructive"
                          : "outline"
                  }
                  className="mt-1"
                >
                  {change.type}
                </Badge>
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-1">
                    <span className="font-medium text-sm">{change.category}</span>
                    <span className="text-xs text-muted-foreground">Page {change.pageNumber}</span>
                    <Badge
                      variant={
                        change.impact === "high" ? "destructive" : change.impact === "medium" ? "secondary" : "outline"
                      }
                      className="text-xs"
                    >
                      {change.impact}
                    </Badge>
                  </div>
                  <p className="text-sm text-muted-foreground">{change.description}</p>
                  {change.oldValue != null && change.newValue != null && (
                    <div className="mt-2 space-y-1">
                      <div className="text-xs">
                        <span className="text-red-600">- {String(change.oldValue)}</span>
                      </div>
                      <div className="text-xs">
                        <span className="text-green-600">+ {String(change.newValue)}</span>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </Card>
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className="h-full flex flex-col">
      <div className="p-4 border-b space-y-4">
        <div className="flex items-center justify-between">
          <h3 className="font-medium">Version Control</h3>
          <div className="flex gap-2">
            <Dialog>
              <DialogTrigger asChild>
                <Button size="sm" variant="outline">
                  <GitBranch className="h-4 w-4 mr-2" />
                  Branch
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Create New Branch</DialogTitle>
                </DialogHeader>
                <div className="space-y-4">
                  <div>
                    <label className="text-sm font-medium">Branch Name</label>
                    <Input
                      value={newBranchData.name}
                      onChange={(e) => setNewBranchData((prev) => ({ ...prev, name: e.target.value }))}
                      placeholder="feature/new-feature"
                    />
                  </div>
                  <div>
                    <label className="text-sm font-medium">Description</label>
                    <Textarea
                      value={newBranchData.description}
                      onChange={(e) => setNewBranchData((prev) => ({ ...prev, description: e.target.value }))}
                      placeholder="Describe the purpose of this branch"
                    />
                  </div>
                  <div>
                    <label className="text-sm font-medium">From Version</label>
                    <Select
                      value={newBranchData.fromVersion}
                      onValueChange={(value) => setNewBranchData((prev) => ({ ...prev, fromVersion: value }))}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select base version" />
                      </SelectTrigger>
                      <SelectContent>
                        {versions.map((version) => (
                          <SelectItem key={version.id} value={version.id}>
                            {version.version} - {version.title}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <Button onClick={createNewBranch} disabled={isCreatingBranch} className="w-full">
                    {isCreatingBranch ? "Creating..." : "Create Branch"}
                  </Button>
                </div>
              </DialogContent>
            </Dialog>

            <Dialog>
              <DialogTrigger asChild>
                <Button size="sm">
                  <Plus className="h-4 w-4 mr-2" />
                  Version
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Create New Version</DialogTitle>
                </DialogHeader>
                <div className="space-y-4">
                  <div>
                    <label className="text-sm font-medium">Version Title</label>
                    <Input
                      value={newVersionData.title}
                      onChange={(e) => setNewVersionData((prev) => ({ ...prev, title: e.target.value }))}
                      placeholder="Brief description of changes"
                    />
                  </div>
                  <div>
                    <label className="text-sm font-medium">Description</label>
                    <Textarea
                      value={newVersionData.description}
                      onChange={(e) => setNewVersionData((prev) => ({ ...prev, description: e.target.value }))}
                      placeholder="Detailed description of changes made"
                    />
                  </div>
                  <div className="flex items-center gap-2">
                    <input
                      type="checkbox"
                      id="isMajor"
                      checked={newVersionData.isMajor}
                      onChange={(e) => setNewVersionData((prev) => ({ ...prev, isMajor: e.target.checked }))}
                    />
                    <label htmlFor="isMajor" className="text-sm">
                      Major version
                    </label>
                  </div>
                  <Button onClick={createNewVersion} disabled={isCreatingVersion} className="w-full">
                    {isCreatingVersion ? "Creating..." : "Create Version"}
                  </Button>
                </div>
              </DialogContent>
            </Dialog>
          </div>
        </div>

        {/* Branch Selector */}
        <div className="space-y-2">
          <label className="text-sm font-medium">Current Branch</label>
          <Select value={currentBranch} onValueChange={setCurrentBranch}>
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              {branches.map((branch) => (
                <SelectItem key={branch.id} value={branch.id}>
                  <div className="flex items-center gap-2">
                    <GitBranch className="h-4 w-4" />
                    <span>{branch.name}</span>
                    {branch.isDefault && (
                      <Badge variant="outline" className="text-xs">
                        default
                      </Badge>
                    )}
                    {branch.isProtected && <Lock className="h-3 w-3" />}
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Filters */}
        <div className="flex gap-2">
          <Select value={filterType} onValueChange={(value: string) => setFilterType(value as "all" | "major" | "minor" | "tagged")}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All</SelectItem>
              <SelectItem value="major">Major</SelectItem>
              <SelectItem value="minor">Minor</SelectItem>
              <SelectItem value="tagged">Tagged</SelectItem>
            </SelectContent>
          </Select>
          <Input
            placeholder="Search versions..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="flex-1"
          />
        </div>
      </div>

      <Tabs defaultValue="history" className="flex-1 flex flex-col">
        <TabsList className="mx-4 mt-2">
          <TabsTrigger value="history">History</TabsTrigger>
          <TabsTrigger value="compare">Compare</TabsTrigger>
          <TabsTrigger value="branches">Branches</TabsTrigger>
        </TabsList>

        <TabsContent value="history" className="flex-1 overflow-hidden">
          <ScrollArea className="h-full px-4">
            <div className="space-y-3 pb-4">
              {filteredVersions.map((version) => (
                <Card key={version.id} className={cn("p-4", version.id === currentVersion && "ring-2 ring-primary")}>
                  <div className="flex items-start justify-between">
                    <div className="flex items-start gap-3 flex-1">
                      <Avatar className="h-8 w-8">
                        <AvatarImage src={version.author.avatar || "/placeholder.svg"} />
                        <AvatarFallback>
                          {version.author.name
                            .split(" ")
                            .map((n) => n[0])
                            .join("")}
                        </AvatarFallback>
                      </Avatar>
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-1">
                          <span className="font-medium">{version.version}</span>
                          {version.isMajor && <Badge variant="default">Major</Badge>}
                          {version.isLocked && <Lock className="h-3 w-3" />}
                          {version.id === currentVersion && <Badge variant="outline">Current</Badge>}
                        </div>
                        <h4 className="font-medium text-sm mb-1">{version.title}</h4>
                        <p className="text-xs text-muted-foreground mb-2">{version.description}</p>
                        <div className="flex items-center gap-4 text-xs text-muted-foreground">
                          <span>{version.author.name}</span>
                          <span>{version.timestamp.toLocaleDateString()}</span>
                          <span>{Math.round(version.size / 1024)} KB</span>
                          <span>{version.metadata.pageCount} pages</span>
                        </div>
                        {version.tags.length > 0 && (
                          <div className="flex gap-1 mt-2">
                            {version.tags.map((tag) => (
                              <Badge key={tag} variant="secondary" className="text-xs">
                                <Tag className="h-2 w-2 mr-1" />
                                {tag}
                              </Badge>
                            ))}
                          </div>
                        )}
                        {version.changes.length > 0 && (
                          <div className="mt-2">
                            <Badge variant="outline" className="text-xs">
                              {version.changes.length} changes
                            </Badge>
                          </div>
                        )}
                      </div>
                    </div>
                    <div className="flex gap-1">
                      <Button
                        size="sm"
                        variant="ghost"
                        onClick={() => onVersionChange(version.id)}
                        disabled={version.id === currentVersion}
                      >
                        <Eye className="h-4 w-4" />
                      </Button>
                      <Button
                        size="sm"
                        variant="ghost"
                        onClick={() => restoreVersion(version.id)}
                        disabled={version.id === currentVersion || version.isLocked}
                      >
                        <RotateCcw className="h-4 w-4" />
                      </Button>
                      <Button size="sm" variant="ghost" onClick={() => toggleVersionLock(version.id)}>
                        {version.isLocked ? <Unlock className="h-4 w-4" /> : <Lock className="h-4 w-4" />}
                      </Button>
                    </div>
                  </div>
                </Card>
              ))}
            </div>
          </ScrollArea>
        </TabsContent>

        <TabsContent value="compare" className="flex-1 overflow-hidden">
          <div className="p-4 space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="text-sm font-medium">Version A</label>
                <Select
                  value={selectedVersions[0] || ""}
                  onValueChange={(value) => setSelectedVersions((prev) => [value, prev[1] || ""])}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select version" />
                  </SelectTrigger>
                  <SelectContent>
                    {versions.map((version) => (
                      <SelectItem key={version.id} value={version.id}>
                        {version.version} - {version.title}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div>
                <label className="text-sm font-medium">Version B</label>
                <Select
                  value={selectedVersions[1] || ""}
                  onValueChange={(value) => setSelectedVersions((prev) => [prev[0] || "", value])}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select version" />
                  </SelectTrigger>
                  <SelectContent>
                    {versions.map((version) => (
                      <SelectItem key={version.id} value={version.id}>
                        {version.version} - {version.title}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            {selectedVersions[0] && selectedVersions[1] && (
              <ScrollArea className="h-96">
                {renderVersionDiff(
                  versions.find((v) => v.id === selectedVersions[0])!,
                  versions.find((v) => v.id === selectedVersions[1])!,
                )}
              </ScrollArea>
            )}
          </div>
        </TabsContent>

        <TabsContent value="branches" className="flex-1 overflow-hidden">
          <ScrollArea className="h-full px-4">
            <div className="space-y-3 pb-4">
              {branches.map((branch) => (
                <Card key={branch.id} className={cn("p-4", branch.id === currentBranch && "ring-2 ring-primary")}>
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-1">
                        <GitBranch className="h-4 w-4" />
                        <span className="font-medium">{branch.name}</span>
                        {branch.isDefault && <Badge variant="default">Default</Badge>}
                        {branch.isProtected && <Badge variant="secondary">Protected</Badge>}
                      </div>
                      <p className="text-sm text-muted-foreground mb-2">{branch.description}</p>
                      <div className="flex items-center gap-4 text-xs text-muted-foreground">
                        <span>Created by {branch.createdBy}</span>
                        <span>{branch.createdAt.toLocaleDateString()}</span>
                        <span>Last: {branch.lastCommit}</span>
                      </div>
                      {(branch.ahead > 0 || branch.behind > 0) && (
                        <div className="flex gap-2 mt-2">
                          {branch.ahead > 0 && (
                            <Badge variant="outline" className="text-xs">
                              <ArrowRight className="h-2 w-2 mr-1" />
                              {branch.ahead} ahead
                            </Badge>
                          )}
                          {branch.behind > 0 && (
                            <Badge variant="outline" className="text-xs">
                              <ArrowLeft className="h-2 w-2 mr-1" />
                              {branch.behind} behind
                            </Badge>
                          )}
                        </div>
                      )}
                    </div>
                    <div className="flex gap-1">
                      <Button
                        size="sm"
                        variant="ghost"
                        onClick={() => setCurrentBranch(branch.id)}
                        disabled={branch.id === currentBranch}
                      >
                        <GitCommit className="h-4 w-4" />
                      </Button>
                      {branch.ahead > 0 && (
                        <Button size="sm" variant="ghost">
                          <GitMerge className="h-4 w-4" />
                        </Button>
                      )}
                    </div>
                  </div>
                </Card>
              ))}
            </div>
          </ScrollArea>
        </TabsContent>
      </Tabs>
    </div>
  )
}

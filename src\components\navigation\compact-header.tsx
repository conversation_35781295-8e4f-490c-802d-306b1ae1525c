"use client";

import React, { useState, useCallback } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Separator } from '@/components/ui/separator';
import {
  ChevronLeft,
  ChevronRight,
  ZoomIn,
  ZoomOut,
  RotateCcw,
  Download,
  X,
  Maximize,
  Minimize,
  Menu,
  Bookmark,
  BookmarkPlus,
  Sidebar,
  PenTool,
  Grid3X3,
  Printer,
  FileText,
  Edit,
  GitBranch,
  Search,
  Settings,
  MoreHorizontal,
  ChevronDown,
  ChevronUp
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { cn } from '@/lib/utils';

interface CompactHeaderProps {
  // Document info
  title?: string;
  pageNumber: number;
  numPages: number;
  scale: number;
  rotation: number;
  isFullscreen: boolean;
  
  // Navigation
  onPageChange: (page: number) => void;
  onPrevPage: () => void;
  onNextPage: () => void;
  canGoToPrev: boolean;
  canGoToNext: boolean;
  
  // View controls
  onZoomIn: () => void;
  onZoomOut: () => void;
  onResetZoom: () => void;
  onRotate: () => void;
  onToggleFullscreen: () => void;
  
  // Actions
  onClose: () => void;
  onDownload: () => void;
  onOpenSidebar: (tab?: string) => void;
  
  // Toolbar state
  selectedTool?: string | null;
  onToolSelect: (tool: string | null) => void;
  
  // Bookmarks
  isPageBookmarked: boolean;
  onAddBookmark: () => void;
  
  // Layout options
  isCompact?: boolean;
  showAdvancedTools?: boolean;
  className?: string;
}

export default function CompactHeader({
  title,
  pageNumber,
  numPages,
  scale,
  rotation,
  isFullscreen,
  onPageChange,
  onPrevPage,
  onNextPage,
  canGoToPrev,
  canGoToNext,
  onZoomIn,
  onZoomOut,
  onResetZoom,
  onRotate,
  onToggleFullscreen,
  onClose,
  onDownload,
  onOpenSidebar,
  selectedTool,
  onToolSelect,
  isPageBookmarked,
  onAddBookmark,
  isCompact = false,
  showAdvancedTools = false,
  className
}: CompactHeaderProps) {
  const [isExpanded, setIsExpanded] = useState(!isCompact);
  const [showMoreTools, setShowMoreTools] = useState(false);

  const handlePageInputChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const value = parseInt(e.target.value);
    if (value >= 1 && value <= numPages) {
      onPageChange(value);
    }
  }, [numPages, onPageChange]);

  const toggleExpanded = useCallback(() => {
    setIsExpanded(!isExpanded);
  }, [isExpanded]);

  // Core navigation tools (always visible)
  const coreTools = (
    <div className="flex items-center gap-1">
      <Button
        variant="ghost"
        size="sm"
        onClick={onPrevPage}
        disabled={!canGoToPrev}
        className="h-7 w-7 p-0"
        title="Previous page"
      >
        <ChevronLeft className="h-3 w-3" />
      </Button>

      <div className="flex items-center gap-1 mx-1">
        <Input
          type="number"
          value={pageNumber}
          onChange={handlePageInputChange}
          className="w-12 h-7 text-xs text-center p-1"
          min={1}
          max={numPages}
        />
        <span className="text-xs text-muted-foreground whitespace-nowrap">
          /{numPages}
        </span>
      </div>

      <Button
        variant="ghost"
        size="sm"
        onClick={onNextPage}
        disabled={!canGoToNext}
        className="h-7 w-7 p-0"
        title="Next page"
      >
        <ChevronRight className="h-3 w-3" />
      </Button>
    </div>
  );

  // Zoom controls
  const zoomControls = (
    <div className="flex items-center gap-1">
      <Button
        variant="ghost"
        size="sm"
        onClick={onZoomOut}
        disabled={scale <= 0.5}
        className="h-7 w-7 p-0"
        title="Zoom out"
      >
        <ZoomOut className="h-3 w-3" />
      </Button>

      <span className="text-xs font-medium min-w-[3rem] text-center">
        {Math.round(scale * 100)}%
      </span>

      <Button
        variant="ghost"
        size="sm"
        onClick={onZoomIn}
        disabled={scale >= 3.0}
        className="h-7 w-7 p-0"
        title="Zoom in"
      >
        <ZoomIn className="h-3 w-3" />
      </Button>

      <Button
        variant="ghost"
        size="sm"
        onClick={onResetZoom}
        className="h-7 px-2 text-xs"
        title="Reset zoom"
      >
        Fit
      </Button>
    </div>
  );

  return (
    <TooltipProvider>
      <div className={cn(
        "border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60",
        className
      )}>
        {/* Main header row - always visible */}
        <div className="flex items-center justify-between px-2 py-1 min-h-[2.5rem]">
          {/* Left section */}
          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={onClose}
              className="h-7 w-7 p-0"
              title="Close"
            >
              <X className="h-3 w-3" />
            </Button>

            <Button
              variant="ghost"
              size="sm"
              onClick={() => onOpenSidebar()}
              className="h-7 w-7 p-0"
              title="Open sidebar"
            >
              <Sidebar className="h-3 w-3" />
            </Button>

            {title && !isCompact && (
              <h1 className="font-medium text-sm truncate max-w-[200px]">
                {title}
              </h1>
            )}
          </div>

          {/* Center section - Core navigation */}
          <div className="flex items-center gap-2">
            {coreTools}
            
            {!isCompact && (
              <>
                <Separator orientation="vertical" className="h-4" />
                {zoomControls}
              </>
            )}
          </div>

          {/* Right section */}
          <div className="flex items-center gap-1">
            {/* Essential tools */}
            <Button
              variant="ghost"
              size="sm"
              onClick={onRotate}
              className="h-7 w-7 p-0"
              title="Rotate"
            >
              <RotateCcw className="h-3 w-3" />
            </Button>

            <Button
              variant="ghost"
              size="sm"
              onClick={onAddBookmark}
              className={cn("h-7 w-7 p-0", isPageBookmarked && "text-yellow-600")}
              title={isPageBookmarked ? "Remove bookmark" : "Add bookmark"}
            >
              {isPageBookmarked ? (
                <Bookmark className="h-3 w-3 fill-current" />
              ) : (
                <BookmarkPlus className="h-3 w-3" />
              )}
            </Button>

            {/* More tools dropdown */}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-7 w-7 p-0"
                  title="More tools"
                >
                  <MoreHorizontal className="h-3 w-3" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-48">
                <DropdownMenuItem onClick={() => onOpenSidebar('annotations')}>
                  <PenTool className="h-4 w-4 mr-2" />
                  Annotations
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => onOpenSidebar('search')}>
                  <Search className="h-4 w-4 mr-2" />
                  Search
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => onOpenSidebar('thumbnails')}>
                  <Grid3X3 className="h-4 w-4 mr-2" />
                  Thumbnails
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={() => onOpenSidebar('print')}>
                  <Printer className="h-4 w-4 mr-2" />
                  Print
                </DropdownMenuItem>
                <DropdownMenuItem onClick={onDownload}>
                  <Download className="h-4 w-4 mr-2" />
                  Download
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={onToggleFullscreen}>
                  {isFullscreen ? (
                    <>
                      <Minimize className="h-4 w-4 mr-2" />
                      Exit Fullscreen
                    </>
                  ) : (
                    <>
                      <Maximize className="h-4 w-4 mr-2" />
                      Fullscreen
                    </>
                  )}
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>

            {/* Expand/collapse toggle for compact mode */}
            {isCompact && (
              <Button
                variant="ghost"
                size="sm"
                onClick={toggleExpanded}
                className="h-7 w-7 p-0"
                title={isExpanded ? "Collapse toolbar" : "Expand toolbar"}
              >
                {isExpanded ? (
                  <ChevronUp className="h-3 w-3" />
                ) : (
                  <ChevronDown className="h-3 w-3" />
                )}
              </Button>
            )}
          </div>
        </div>

        {/* Expanded toolbar row - shown when expanded or not in compact mode */}
        {(isExpanded || !isCompact) && showAdvancedTools && (
          <div className="border-t px-2 py-1 bg-muted/20">
            <div className="flex items-center justify-between">
              {/* Advanced tools */}
              <div className="flex items-center gap-1">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => onOpenSidebar('forms')}
                  className="h-7 px-2 text-xs"
                  title="Forms"
                >
                  <FileText className="h-3 w-3 mr-1" />
                  Forms
                </Button>

                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => onOpenSidebar('form-designer')}
                  className="h-7 px-2 text-xs"
                  title="Form Designer"
                >
                  <Edit className="h-3 w-3 mr-1" />
                  Design
                </Button>

                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => onOpenSidebar('workflows')}
                  className="h-7 px-2 text-xs"
                  title="Workflows"
                >
                  <GitBranch className="h-3 w-3 mr-1" />
                  Workflow
                </Button>
              </div>

              {/* Zoom controls for compact mode */}
              {isCompact && (
                <div className="flex items-center gap-1">
                  {zoomControls}
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </TooltipProvider>
  );
}

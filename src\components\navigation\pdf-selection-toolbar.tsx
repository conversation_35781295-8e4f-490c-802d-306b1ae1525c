"use client";

import { useState, useCallback } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import {
  Copy,
  Search,
  Bookmark,
  HighlighterIcon as Highlight,
  X,
  Type,
} from "lucide-react";
import { toast } from "sonner";

interface TextSelection {
  text: string;
  pageNumber: number;
  startIndex: number;
  endIndex: number;
}

interface PDFSelectionToolbarProps {
  selection: TextSelection | null;
  onSearch?: (text: string) => void;
  onBookmark?: () => void;
  onHighlight?: (text: string) => void;
  onClear?: () => void;
}

export default function PDFSelectionToolbar({
  selection,
  onSearch,
  onBookmark,
  onHighlight,
  onClear,
}: PDFSelectionToolbarProps) {
  const [copied, setCopied] = useState(false);

  const copyText = useCallback(async () => {
    if (!selection?.text) return;

    try {
      await navigator.clipboard.writeText(selection.text);
      setCopied(true);
      toast(`Copied ${selection.text.length} characters to clipboard.`);

      setTimeout(() => setCopied(false), 2000);
    } catch {
      toast.error("Unable to copy text to clipboard.");
    }
  }, [selection]);

  const searchText = useCallback(() => {
    if (selection?.text && onSearch) {
      onSearch(selection.text);
      toast(
        `Searching for "${selection.text.substring(0, 30)}${
          selection.text.length > 30 ? "..." : ""
        }"`
      );
    }
  }, [selection, onSearch]);

  const highlightText = useCallback(() => {
    if (selection?.text && onHighlight) {
      onHighlight(selection.text);
      toast("Selected text has been highlighted.");
    }
  }, [selection, onHighlight]);

  if (!selection) return null;

  const truncatedText =
    selection.text.length > 100
      ? selection.text.substring(0, 100) + "..."
      : selection.text;

  return (
    <Card className="fixed bottom-4 left-1/2 transform -translate-x-1/2 z-50 shadow-lg animate-in slide-in-from-bottom-2 duration-300">
      <div className="p-4 space-y-3">
        {/* Selection Info */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Type className="h-4 w-4 text-muted-foreground" />
            <span className="text-sm font-medium">Text Selected</span>
            <Badge variant="secondary" className="text-xs">
              Page {selection.pageNumber}
            </Badge>
            <Badge variant="outline" className="text-xs">
              {selection.text.length} chars
            </Badge>
          </div>
          <Button variant="ghost" size="sm" onClick={onClear}>
            <X className="h-4 w-4" />
          </Button>
        </div>

        {/* Selected Text Preview */}
        <div className="bg-muted/50 rounded p-2 text-sm max-w-md">
          <p className="text-muted-foreground italic">&quot;{truncatedText}&quot;</p>
        </div>

        <Separator />

        {/* Action Buttons */}
        <div className="flex items-center gap-2">
          <Button
            size="sm"
            onClick={copyText}
            variant={copied ? "default" : "outline"}
          >
            <Copy className="h-4 w-4 mr-2" />
            {copied ? "Copied!" : "Copy"}
          </Button>

          <Button size="sm" variant="outline" onClick={searchText}>
            <Search className="h-4 w-4 mr-2" />
            Search
          </Button>

          <Button size="sm" variant="outline" onClick={highlightText}>
            <Highlight className="h-4 w-4 mr-2" />
            Highlight
          </Button>

          <Button size="sm" variant="outline" onClick={onBookmark}>
            <Bookmark className="h-4 w-4 mr-2" />
            Bookmark
          </Button>
        </div>

        {/* Keyboard Shortcut Hint */}
        <div className="text-xs text-muted-foreground text-center">
          Press{" "}
          <kbd className="px-1 py-0.5 bg-muted rounded text-xs">Ctrl+C</kbd> to
          copy selected text
        </div>
      </div>
    </Card>
  );
}

# Cobalt PDF Viewer

A modern, feature-rich PDF viewer and document management system built with Next.js, React, and TypeScript. Designed for both individual document viewing and comprehensive document library management.

## Features

### Core PDF Viewing
- 📄 **PDF Viewing**: High-quality PDF rendering with zoom, rotation, and navigation
- 🔍 **Search**: Full-text search within PDF documents
- 📑 **Bookmarks**: Add, edit, and navigate bookmarks
- 🖼️ **Thumbnails**: Page thumbnail navigation
- 📱 **Responsive**: Mobile-friendly design with touch support
- 🎨 **Modern UI**: Clean, accessible interface built with shadcn/ui
- ⚡ **Performance**: Optimized rendering and memory management

### Document Management
- 📚 **Document Library**: Comprehensive document management with metadata
- 🏷️ **Organization**: Tags, categories, collections, and favorites
- 🔍 **Advanced Search**: Full-text search with faceted filtering
- 📊 **Multiple Views**: Grid, list, and compact view modes
- 📤 **Import/Export**: Bulk import from files/URLs and export collections
- ⚙️ **Settings**: Customizable preferences and storage management
- 🗂️ **Collections**: System and custom collections for organization
- 📋 **Bulk Operations**: Multi-select and bulk editing capabilities

### Advanced Features
- 🔧 **Multi-Document**: Open and manage multiple PDFs simultaneously
- 💾 **Persistence**: IndexedDB storage for offline document library
- 🎯 **Drag & Drop**: Intuitive document organization
- 📈 **Usage Analytics**: Track document access and usage patterns
- 🔒 **Validation**: Advanced PDF file validation and error handling
- 🚀 **Extensible**: Modular architecture for additional features

## Getting Started

### Prerequisites

- Node.js 18+
- npm, yarn, pnpm, or bun

### Installation

1. Clone the repository:
```bash
git clone https://github.com/your-username/cobalt-pdf-viewer.git
cd cobalt-pdf-viewer
```

2. Install dependencies:
```bash
npm install
# or
yarn install
# or
pnpm install
```

3. Run the development server:
```bash
npm run dev
# or
yarn dev
# or
pnpm dev
```

4. Open [http://localhost:3000](http://localhost:3000) in your browser.

## Usage

### Basic PDF Viewing

1. **Upload a PDF**: Use the upload interface to select a PDF file or provide a URL
2. **Navigate**: Use the toolbar controls to navigate pages, zoom, and rotate
3. **Search**: Use the search functionality to find text within the document
4. **Bookmarks**: Add bookmarks to important pages for quick navigation

### Document Library Management

1. **Access Library**: Click the "Documents" tab in the sidebar to access your document library
2. **Add Documents**: Upload PDFs with automatic metadata extraction and organization
3. **Organize**: Use tags, categories, and collections to organize your documents
4. **Search & Filter**: Use advanced search and filtering to find specific documents
5. **Bulk Operations**: Select multiple documents for bulk editing and organization

### Advanced Features

- **Multi-Document Viewing**: Open multiple PDFs in tabs for easy comparison
- **Import/Export**: Bulk import documents and export your library for backup
- **Settings**: Customize view preferences, storage limits, and automation settings

## API Documentation

### Document Library API

The document library provides a comprehensive API for managing PDF documents:

```typescript
import { documentLibrary } from '@/lib/document-library';

// Add a document to the library
const document = await documentLibrary.addDocument(file, metadata);

// Search documents
const results = await documentLibrary.searchDocuments({
  text: 'search term',
  tags: ['important'],
  categories: ['reports']
});

// Get all documents
const documents = await documentLibrary.getAllDocuments();

// Update document metadata
await documentLibrary.updateDocument(documentId, {
  metadata: { ...updatedMetadata }
});
```

### Component Usage

#### DocumentLibrary Component

```tsx
import { DocumentLibrary } from '@/components/library';

function MyApp() {
  return (
    <DocumentLibrary
      onDocumentSelect={(document) => console.log('Selected:', document)}
      onDocumentOpen={(document) => console.log('Opening:', document)}
    />
  );
}
```

#### DocumentLibrarySidebar Component

```tsx
import { DocumentLibrarySidebar } from '@/components/library';

function Sidebar() {
  return (
    <DocumentLibrarySidebar
      onDocumentSelect={handleDocumentSelect}
      onDocumentOpen={handleDocumentOpen}
      onNewDocument={handleNewDocument}
    />
  );
}
```

## Architecture

### Project Structure

```
src/
├── components/
│   ├── core/                 # Core PDF viewing components
│   │   ├── pdf-viewer.tsx
│   │   ├── pdf-upload.tsx
│   │   └── document-manager.tsx
│   ├── library/              # Document management components
│   │   ├── document-library.tsx
│   │   ├── document-organizer.tsx
│   │   ├── advanced-filters.tsx
│   │   ├── document-import-export.tsx
│   │   └── document-settings.tsx
│   ├── navigation/           # Navigation and sidebar components
│   └── ui/                   # Reusable UI components
├── lib/
│   ├── types/pdf.ts         # TypeScript type definitions
│   ├── document-library.ts  # Document storage and management
│   └── utils.ts             # Utility functions
└── app/                     # Next.js app directory
```

### Key Technologies

- **Next.js 14**: React framework with App Router
- **TypeScript**: Type-safe development
- **PDF.js**: PDF rendering and manipulation
- **IndexedDB**: Client-side document storage
- **shadcn/ui**: Modern UI component library
- **Tailwind CSS**: Utility-first CSS framework
- **Lucide React**: Icon library

## Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/amazing-feature`
3. Commit your changes: `git commit -m 'Add amazing feature'`
4. Push to the branch: `git push origin feature/amazing-feature`
5. Open a Pull Request

### Development Guidelines

- Follow TypeScript best practices
- Use the existing component patterns
- Add tests for new functionality
- Update documentation for new features
- Ensure responsive design compatibility

## Testing

Run the test suite:

```bash
npm test
# or
yarn test
# or
pnpm test
```

Run tests in watch mode:

```bash
npm run test:watch
```

## Deployment

### Vercel (Recommended)

The easiest way to deploy is using [Vercel](https://vercel.com):

1. Push your code to GitHub
2. Connect your repository to Vercel
3. Deploy automatically on every push

### Other Platforms

The application can be deployed to any platform that supports Next.js:

- **Netlify**: Use the Next.js build command
- **AWS**: Deploy using AWS Amplify or EC2
- **Docker**: Use the included Dockerfile

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## Acknowledgments

- [PDF.js](https://mozilla.github.io/pdf.js/) for PDF rendering capabilities
- [shadcn/ui](https://ui.shadcn.com/) for the beautiful UI components
- [Next.js](https://nextjs.org/) for the excellent React framework
- [Vercel](https://vercel.com/) for hosting and deployment platform
